<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Comment;
use Illuminate\Support\Facades\Auth;

class CommentController extends Controller
{
    /**
     * Store a newly created comment in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'insight_id' => 'required|exists:insights,id',
            'content' => 'required|string|max:500',
            'parent_comment_id' => 'nullable|exists:comments,id'
        ]);

        Comment::create([
            'user_id' => Auth::id(),
            'insight_id' => $request->insight_id,
            'content' => $request->content,
            'parent_comment_id' => $request->parent_comment_id,
            'is_valid' => true, // Assuming comments are valid by default
        ]);

        return back()->with('success', 'Comment posted successfully!');
    }
}
