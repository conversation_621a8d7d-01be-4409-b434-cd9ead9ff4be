<?php

namespace App\Http\Controllers;

use App\Models\Insight;
use App\Models\InsightSave;
use Illuminate\Support\Facades\Auth;

class InsightSaveController extends Controller
{
    public function toggle(Insight $insight)
    {
        $user = Auth::user();
        
        $existingSave = InsightSave::where('user_id', $user->id)
                                  ->where('insight_id', $insight->id)
                                  ->first();
        
        if ($existingSave) {
            // Unsave
            $existingSave->delete();
            $insight->decrement('saves_count');
            $saved = false;
        } else {
            // Save
            InsightSave::create([
                'user_id' => $user->id,
                'insight_id' => $insight->id,
            ]);
            $insight->increment('saves_count');
            $saved = true;
        }
        
        return response()->json([
            'saved' => $saved,
            'saves_count' => $insight->fresh()->saves_count,
            'message' => $saved ? 'Insight saved successfully' : 'Insight removed from saves'
        ]);
    }

    public function index()
    {
        $user = Auth::user();

        // Get saved insights
        $savedInsights = $user->savedInsights()
                             ->with(['user', 'category'])
                             ->where('status', 'published')
                             ->latest('insight_saves.created_at')
                             ->paginate(5);

        // Get saved articles
        $savedArticles = $user->savedArticles()
                             ->with(['user', 'category'])
                             ->where('status', 'published')
                             ->latest('article_saves.created_at')
                             ->paginate(5);

        // Get saved education content
        $savedEducation = $user->savedEducation()
                              ->with(['user', 'category'])
                              ->where('status', 'published')
                              ->latest('education_saves.created_at')
                              ->paginate(5);

        return view('saves.index', compact('savedInsights', 'savedArticles', 'savedEducation'));
    }
}
