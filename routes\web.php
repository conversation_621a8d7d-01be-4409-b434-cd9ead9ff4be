<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\InsightController;
use App\Http\Controllers\ArticleController;
use App\Http\Controllers\CommentController;
use App\Http\Controllers\InsightLikeController;
use App\Http\Controllers\InsightSaveController;
use App\Http\Controllers\EducationController;
use App\Http\Controllers\CommunityController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\StockController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\ArticleSaveController;
use App\Http\Controllers\EducationSaveController;

// --- Authentication Routes ---
Route::get('auth', [AuthController::class, 'showAuthForm'])->name('auth');
Route::get('login', [AuthController::class, 'showAuthForm'])->name('login');
Route::get('register', [AuthController::class, 'showAuthForm'])->name('register');
Route::post('login', [AuthController::class, 'login']);
Route::post('register', [AuthController::class, 'register']);
Route::post('logout', [AuthController::class, 'logout'])->name('logout')->middleware('auth');

// --- Public Article Routes ---
Route::get('/articles', [ArticleController::class, 'index'])->name('articles.index');


// --- Authenticated User Routes ---
Route::middleware(['auth'])->group(function () {
    Route::get('/', [HomeController::class, 'index'])->name('home');

    // Insight features
    Route::post('/insights', [InsightController::class, 'store'])->name('insights.store');
    Route::post('/insights/{insight}/like', [InsightLikeController::class, 'toggle'])->name('insights.like');
    Route::post('/insights/{insight}/save', [InsightSaveController::class, 'toggle'])->name('insights.save');

    // Article Save/Unsave
    Route::post('/articles/{article}/save', [ArticleSaveController::class, 'toggle'])->name('articles.save');

    // Education Save/Unsave
    Route::post('/education/{education}/save', [EducationSaveController::class, 'toggle'])->name('education.save');

    // Comment feature
    Route::post('/comments', [CommentController::class, 'store'])->name('comments.store');

    // Articles
    Route::get('/articles/create', [ArticleController::class, 'create'])->name('articles.create');
    Route::post('/articles', [ArticleController::class, 'store'])->name('articles.store');
    Route::get('/articles/my-articles', [ArticleController::class, 'myArticles'])->name('articles.my-articles');
    Route::get('/articles/{article}/edit', [ArticleController::class, 'edit'])->name('articles.edit');
    Route::put('/articles/{article}', [ArticleController::class, 'update'])->name('articles.update');
    Route::delete('/articles/{article}', [ArticleController::class, 'destroy'])->name('articles.destroy');

    Route::get('/articles/{article}', [ArticleController::class, 'show'])->name('articles.show');

    // Education
    Route::get('/education', [EducationController::class, 'index'])->name('education.index');
    Route::get('/education/create', [EducationController::class, 'create'])->name('education.create');
    Route::post('/education', [EducationController::class, 'store'])->name('education.store');
    Route::get('/education/my-content', [EducationController::class, 'myContent'])->name('education.my-content');
    Route::get('/education/{education}/edit', [EducationController::class, 'edit'])->name('education.edit');
    Route::put('/education/{education}', [EducationController::class, 'update'])->name('education.update');
    Route::delete('/education/{education}', [EducationController::class, 'destroy'])->name('education.destroy');

    Route::get('/education/{education}', [EducationController::class, 'show'])->name('education.show');

    // Other navigation pages
    Route::get('/saves', [InsightSaveController::class, 'index'])->name('saves.index');
    Route::get('/community', [CommunityController::class, 'index'])->name('community.index');
    Route::get('/profile', [ProfileController::class, 'index'])->name('profile.index');
    Route::put('/profile', [ProfileController::class, 'update'])->name('profile.update');

    // Stock prices API
    Route::get('/api/stock-prices', [StockController::class, 'getStockPrices'])->name('api.stock-prices');

    // Most liked insights API
    Route::get('/api/most-liked-insights', [InsightLikeController::class, 'getMostLiked'])->name('api.most-liked-insights');

    // --- Admin-Only Routes ---
    Route::middleware(['role:Admin'])->prefix('admin')->name('admin.')->group(function () {
        // Admin Dashboard
        Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');

        // User Management
        Route::get('/users', [AdminController::class, 'users'])->name('users');
        Route::get('/users/{user}/details', [AdminController::class, 'getUserDetails'])->name('users.details');
        Route::put('/users/{user}/role', [AdminController::class, 'updateUserRole'])->name('users.update-role');
        Route::put('/users/{user}/verify-email', [AdminController::class, 'verifyUserEmail'])->name('users.verify-email');
        Route::delete('/users/{user}', [AdminController::class, 'deleteUser'])->name('users.delete');

        // Article Management
        Route::get('/articles', [AdminController::class, 'articles'])->name('articles');
        Route::get('/articles/{article}/details', [AdminController::class, 'getArticleDetails'])->name('articles.details');
        Route::put('/articles/{article}/approve', [AdminController::class, 'approveArticle'])->name('articles.approve');
        Route::put('/articles/{article}/reject', [AdminController::class, 'rejectArticle'])->name('articles.reject');
        Route::delete('/articles/{article}', [AdminController::class, 'deleteArticle'])->name('articles.delete');

        // Education Management
        Route::get('/education', [AdminController::class, 'education'])->name('education');
        Route::get('/education/{education}/details', [AdminController::class, 'getEducationDetails'])->name('education.details');
        Route::put('/education/{education}/approve', [AdminController::class, 'approveEducation'])->name('education.approve');
        Route::put('/education/{education}/reject', [AdminController::class, 'rejectEducation'])->name('education.reject');
        Route::delete('/education/{education}', [AdminController::class, 'deleteEducation'])->name('education.delete');
    });
});