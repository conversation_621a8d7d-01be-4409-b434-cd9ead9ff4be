<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('insight_saves', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('insight_id')->constrained('insights')->onDelete('cascade');
            $table->timestamps();
            
            // Ensure a user can only save an insight once
            $table->unique(['user_id', 'insight_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('insight_saves');
    }
};
