@extends('layout')

@section('title', 'Financial News & Articles - InsightFlow')

@section('content')
<div class="p-4 lg:p-8">
    <div class="max-w-7xl mx-auto">
        <!-- Header Section -->
        <div class="mb-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div>
                    <h1 class="text-2xl lg:text-3xl font-bold text-gray-800">Financial News & Articles</h1>
                    <p class="text-gray-600 mt-2">
                        @if($selectedCategory)
                            Showing articles in <span class="font-medium text-blue-600">{{ $selectedCategory->name }}</span> category
                        @else
                            Stay updated with the latest market insights and analysis
                        @endif
                    </p>
                </div>
                <div class="flex items-center gap-3">
                    <x-dropdown
                        trigger="{{ $selectedCategory ? $selectedCategory->name : 'All Categories' }}"
                        :icon="true"
                        button-class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white hover:bg-gray-50 text-gray-700 {{ $selectedCategory ? 'border-blue-500 bg-blue-50' : '' }}"
                    >
                        <x-dropdown-item href="?category=" onclick="filterByCategory('', 'All Categories')">
                            All Categories
                        </x-dropdown-item>
                        @foreach($categories as $category)
                            <x-dropdown-item href="?category={{ $category->id }}" onclick="filterByCategory('{{ $category->id }}', '{{ $category->name }}')">
                                {{ $category->name }}
                            </x-dropdown-item>
                        @endforeach
                    </x-dropdown>

                    @if($selectedCategory)
                        <a href="{{ route('articles.index') }}"
                           class="text-gray-500 hover:text-gray-700 text-sm flex items-center gap-1 transition-colors"
                           title="Clear filter">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                            Clear filter
                        </a>
                    @endif

                    <a href="{{ route('articles.create') }}" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm lg:text-base">
                        Write Article
                    </a>
                </div>
            </div>
        </div>

        <!-- Search Interface -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-4 mb-8">
            <div class="flex flex-col sm:flex-row sm:items-center gap-4">
                <div class="flex-1">
                    <div class="relative">
                        <input type="text"
                               id="search-articles"
                               placeholder="Search articles by title, content, author, or category..."
                               value="{{ request('search') }}"
                               class="w-full border border-gray-300 rounded-lg px-4 py-3 pr-12 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <div class="absolute inset-y-0 right-0 flex items-center pr-4">
                            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                        </div>
                    </div>
                </div>
                <div class="flex items-center gap-3">
                    <div class="text-sm text-gray-500">
                        <span id="articles-count">{{ $articles->total() }}</span> articles found
                    </div>
                    <button id="clear-search" class="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                        Clear
                    </button>
                </div>
            </div>
            <div id="search-loading" class="hidden mt-3">
                <div class="flex items-center text-sm text-gray-500">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                    Searching articles...
                </div>
            </div>
        </div>

        <div id="articles-content-container">
            @include('partials.articles-grid', ['articles' => $articles])
        </div>

        <!-- Pagination -->
        <div id="articles-pagination" class="mt-12">
            @if($articles->hasPages())
                {{ $articles->appends(request()->query())->links() }}
            @endif
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
// Search functionality for articles
let searchTimeout;

function performArticleSearch() {
    const search = document.getElementById('search-articles').value;
    const category = new URLSearchParams(window.location.search).get('category') || '';

    // Show loading indicator
    document.getElementById('search-loading').classList.remove('hidden');

    // Build query parameters
    const params = new URLSearchParams();
    if (search) params.append('search', search);
    if (category) params.append('category', category);

    // Make AJAX request
    fetch(`{{ route('articles.index') }}?${params.toString()}`, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        // Update articles content
        document.getElementById('articles-content-container').innerHTML = data.html;

        // Update pagination
        document.getElementById('articles-pagination').innerHTML = data.pagination;

        // Update count
        document.getElementById('articles-count').textContent = data.total;

        // Update URL without page reload
        const newUrl = `{{ route('articles.index') }}${params.toString() ? '?' + params.toString() : ''}`;
        window.history.pushState({}, '', newUrl);
    })
    .catch(error => {
        console.error('Search error:', error);
        showToast('error', 'Search failed. Please try again.');
    })
    .finally(() => {
        // Hide loading indicator
        document.getElementById('search-loading').classList.add('hidden');
    });
}

// Search input with debouncing
document.getElementById('search-articles').addEventListener('input', function() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(performArticleSearch, 300);
});

// Clear search
document.getElementById('clear-search').addEventListener('click', function() {
    document.getElementById('search-articles').value = '';
    performArticleSearch();
});

// Clear search function for empty state
function clearSearch() {
    document.getElementById('search-articles').value = '';
    performArticleSearch();
}

// Handle pagination clicks
document.addEventListener('click', function(e) {
    if (e.target.closest('#articles-pagination a')) {
        e.preventDefault();
        const url = e.target.closest('a').href;
        const urlParams = new URL(url).searchParams;

        // Add current search and category parameters
        const search = document.getElementById('search-articles').value;
        const category = new URLSearchParams(window.location.search).get('category') || '';

        if (search) urlParams.set('search', search);
        if (category) urlParams.set('category', category);

        // Show loading
        document.getElementById('search-loading').classList.remove('hidden');

        fetch(`{{ route('articles.index') }}?${urlParams.toString()}`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            document.getElementById('articles-content-container').innerHTML = data.html;
            document.getElementById('articles-pagination').innerHTML = data.pagination;
            document.getElementById('articles-count').textContent = data.total;

            // Update URL
            window.history.pushState({}, '', `{{ route('articles.index') }}?${urlParams.toString()}`);

            // Scroll to top of articles
            document.getElementById('articles-content-container').scrollIntoView({ behavior: 'smooth' });
        })
        .catch(error => console.error('Pagination error:', error))
        .finally(() => {
            document.getElementById('search-loading').classList.add('hidden');
        });
    }
});

// Article Save functionality
function toggleArticleSave(articleId) {
    $.post(`/articles/${articleId}/save`)
        .done(function(data) {
            const btn = $(`.article-save-btn[data-article-id="${articleId}"]`);
            const icon = btn.find('svg');

            if (data.saved) {
                btn.addClass('text-green-500').removeClass('text-gray-500');
                icon.attr('fill', 'currentColor');
            } else {
                btn.addClass('text-gray-500').removeClass('text-green-500');
                icon.attr('fill', 'none');
            }

            // Show SweetAlert2 toast
            showToast(data.saved ? 'success' : 'info', data.message);
        })
        .fail(function() {
            showToast('error', 'Error occurred while saving the article');
        });
}

// Category filter function
function filterByCategory(categoryId, categoryName) {
    // Update the dropdown trigger text immediately for better UX
    const trigger = document.querySelector('[x-ref="button"] span');
    if (trigger) {
        trigger.textContent = categoryName || 'All Categories';
    }

    // Update the URL and navigate
    const url = new URL(window.location);
    if (categoryId) {
        url.searchParams.set('category', categoryId);
    } else {
        url.searchParams.delete('category');
    }

    // Preserve search parameter
    const search = document.getElementById('search-articles').value;
    if (search) {
        url.searchParams.set('search', search);
    }

    window.location.href = url.toString();
}
</script>
@endpush