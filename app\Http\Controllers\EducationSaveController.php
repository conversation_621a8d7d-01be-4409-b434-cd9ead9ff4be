<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Education;
use App\Models\EducationSave;
use Illuminate\Support\Facades\Auth;

class EducationSaveController extends Controller
{
    public function toggle(Education $education)
    {
        $user = Auth::user();

        // Check if education content is already saved by this user
        $existingSave = EducationSave::where('user_id', $user->id)
                                    ->where('education_id', $education->id)
                                    ->first();

        if ($existingSave) {
            // Unsave the education content
            $existingSave->delete();
            $saved = false;
            $message = 'Education content removed from saves';
        } else {
            // Save the education content
            EducationSave::create([
                'user_id' => $user->id,
                'education_id' => $education->id
            ]);
            $saved = true;
            $message = 'Education content saved successfully';
        }

        // Get updated save count
        $saveCount = $education->saves()->count();

        return response()->json([
            'saved' => $saved,
            'save_count' => $saveCount,
            'message' => $message
        ]);
    }
}
