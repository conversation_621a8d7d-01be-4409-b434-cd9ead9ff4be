<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Insight extends Model
{
    use HasFactory;

    protected $fillable = ['user_id', 'title', 'content', 'image_url', 'category_id', 'status', 'likes_count', 'saves_count'];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function comments()
    {
        return $this->hasMany(Comment::class);
    }

    public function watchlists()
    {
        return $this->hasMany(Watchlist::class);
    }

    public function likes()
    {
        return $this->hasMany(InsightLike::class);
    }

    public function saves()
    {
        return $this->hasMany(InsightSave::class);
    }

    public function isLikedBy($user)
    {
        if (!$user) return false;
        return $this->likes()->where('user_id', $user->id)->exists();
    }

    public function isSavedBy($user)
    {
        if (!$user) return false;
        return $this->saves()->where('user_id', $user->id)->exists();
    }

    // Scope for most liked insights
    public function scopeMostLiked($query, $limit = 5)
    {
        return $query->orderBy('likes_count', 'desc')->limit($limit);
    }

    // Scope for most saved insights
    public function scopeMostSaved($query, $limit = 5)
    {
        return $query->orderBy('saves_count', 'desc')->limit($limit);
    }
}
