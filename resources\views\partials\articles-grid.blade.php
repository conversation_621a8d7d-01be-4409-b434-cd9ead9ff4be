@if($articles->count() > 0)
    <!-- Featured Article -->
    @php $featuredArticle = $articles->first(); @endphp
    <div class="mb-12">
        <div class="bg-white rounded-lg shadow-sm overflow-hidden">
            <div class="lg:flex">
                <div class="lg:w-1/2">
                    @if($featuredArticle->image_url)
                        <img src="{{ $featuredArticle->image_url }}"
                             alt="{{ $featuredArticle->title }}"
                             class="w-full h-48 lg:h-full object-cover"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                        <div class="w-full h-48 lg:h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center" style="display: none;">
                            <span class="text-white text-4xl font-bold">{{ substr($featuredArticle->title, 0, 1) }}</span>
                        </div>
                    @else
                        <div class="w-full h-48 lg:h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                            <span class="text-white text-4xl font-bold">{{ substr($featuredArticle->title, 0, 1) }}</span>
                        </div>
                    @endif
                </div>
                <div class="lg:w-1/2 p-6 lg:p-8">
                    <div class="flex items-center mb-4">
                        <span class="bg-red-600 text-white text-xs font-bold px-2 py-1 rounded mr-3">FEATURED</span>
                        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">{{ $featuredArticle->category->name }}</span>
                    </div>
                    <h2 class="text-xl lg:text-2xl font-bold text-gray-900 mb-3 leading-tight">{{ $featuredArticle->title }}</h2>
                    <p class="text-gray-600 mb-4 leading-relaxed">{{ Str::limit(strip_tags($featuredArticle->content), 200) }}</p>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-8 h-8 rounded-full bg-blue-100 mr-3 flex items-center justify-center">
                                <span class="text-blue-600 font-semibold text-xs">{{ substr($featuredArticle->user->name, 0, 1) }}</span>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900">{{ $featuredArticle->user->name }}</p>
                                <p class="text-xs text-gray-500">{{ $featuredArticle->created_at->format('M d, Y') }}</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3">
                            @auth
                                <button onclick="toggleArticleSave({{ $featuredArticle->id }})"
                                        class="article-save-btn text-gray-500 hover:text-green-500 transition-colors {{ $featuredArticle->isSavedBy(auth()->id()) ? 'text-green-500' : '' }}"
                                        data-article-id="{{ $featuredArticle->id }}">
                                    <svg class="w-5 h-5" fill="{{ $featuredArticle->isSavedBy(auth()->id()) ? 'currentColor' : 'none' }}" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"/>
                                    </svg>
                                </button>
                            @endauth
                            <a href="{{ route('articles.show', $featuredArticle) }}"
                               class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
                                Read Full Article
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Latest Articles Grid -->
    <div class="mb-8">
        <h2 class="text-xl font-bold text-gray-800 mb-6">Latest Articles</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach($articles->skip(1) as $article)
                <article class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow group">
                    <div class="aspect-w-16 aspect-h-9 bg-gray-200">
                        @if($article->image_url)
                            <img src="{{ $article->image_url }}"
                                 alt="{{ $article->title }}"
                                 class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="w-full h-48 bg-gradient-to-br from-gray-400 to-gray-600 flex items-center justify-center" style="display: none;">
                                <span class="text-white text-2xl font-bold">{{ substr($article->title, 0, 1) }}</span>
                            </div>
                        @else
                            <div class="w-full h-48 bg-gradient-to-br from-gray-400 to-gray-600 flex items-center justify-center">
                                <span class="text-white text-2xl font-bold">{{ substr($article->title, 0, 1) }}</span>
                            </div>
                        @endif
                    </div>
                    <div class="p-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">{{ $article->category->name }}</span>
                            <time class="text-xs text-gray-500">{{ $article->created_at->format('M d') }}</time>
                        </div>
                        <h3 class="text-lg font-bold text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors">
                            <a href="{{ route('articles.show', $article) }}">{{ $article->title }}</a>
                        </h3>
                        <p class="text-gray-600 text-sm mb-3 line-clamp-3">{{ Str::limit(strip_tags($article->content), 120) }}</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-6 h-6 rounded-full bg-blue-100 mr-2 flex items-center justify-center">
                                    <span class="text-blue-600 font-semibold text-xs">{{ substr($article->user->name, 0, 1) }}</span>
                                </div>
                                <span class="text-xs text-gray-600">{{ $article->user->name }}</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                @auth
                                    <button onclick="toggleArticleSave({{ $article->id }})"
                                            class="article-save-btn text-gray-500 hover:text-green-500 transition-colors {{ $article->isSavedBy(auth()->id()) ? 'text-green-500' : '' }}"
                                            data-article-id="{{ $article->id }}">
                                        <svg class="w-4 h-4" fill="{{ $article->isSavedBy(auth()->id()) ? 'currentColor' : 'none' }}" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"/>
                                        </svg>
                                    </button>
                                @endauth
                                <a href="{{ route('articles.show', $article) }}"
                                   class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                                    Read More →
                                </a>
                            </div>
                        </div>
                    </div>
                </article>
            @endforeach
        </div>
    </div>
@else
    <!-- Empty State -->
    <div class="text-center py-16">
        <div class="w-20 h-20 bg-gray-100 rounded-full mx-auto mb-6 flex items-center justify-center">
            <svg class="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
            </svg>
        </div>
        <h3 class="text-xl font-medium text-gray-900 mb-3">No articles found</h3>
        <p class="text-gray-500 mb-6">No articles match your search criteria. Try adjusting your search terms or clear the search to see all articles.</p>
        <button onclick="clearSearch()" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium">
            Clear Search
        </button>
    </div>
@endif
