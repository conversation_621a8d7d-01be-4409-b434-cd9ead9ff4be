<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('insights', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users');
            $table->string('title');
            $table->text('content');
            $table->string('image_url')->nullable();
            $table->foreignId('category_id')->constrained('categories');
            $table->string('status', 50)->default('pending'); // e.g., 'pending', 'published', 'archived'
            $table->integer('likes_count')->default(0);
            $table->integer('saves_count')->default(0);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('insights');
    }
};
