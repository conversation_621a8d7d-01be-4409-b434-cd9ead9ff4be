<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Category;
use App\Models\Insight;

class HomeController extends Controller
{
    /**
     * Show the application dashboard.
     */
    public function index(Request $request)
    {
        // Start with published insights query
        $query = Insight::with(['user', 'category'])
                        ->where('status', 'published');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('content', 'like', "%{$search}%")
                  ->orWhere('title', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        $insights = $query->latest()->paginate(15)->appends($request->query());

        // For AJAX requests, return JSON
        if ($request->ajax()) {
            return response()->json([
                'html' => view('partials.insights-feed', compact('insights'))->render(),
                'pagination' => $insights->links()->toHtml(),
                'total' => $insights->total()
            ]);
        }

        // Fetch categories for the 'new insight' form dropdown
        $insight_categories = Category::where('type', 'insight')->get();

        // Get most liked insights for sidebar
        $mostLikedInsights = Insight::with(['user', 'category'])
                                   ->where('status', 'published')
                                   ->mostLiked(5)
                                   ->get();

        // Get most saved insights for sidebar
        $mostSavedInsights = Insight::with(['user', 'category'])
                                   ->where('status', 'published')
                                   ->mostSaved(5)
                                   ->get();

        // Get category usage statistics
        $categoryStats = Category::where('type', 'insight')
                                ->withCount(['insights' => function($query) {
                                    $query->where('status', 'published');
                                }])
                                ->orderBy('insights_count', 'desc')
                                ->limit(5)
                                ->get();

        // Indonesian stock symbols for TradingView
        $stockSymbols = [
            'IDX:BBCA', 'IDX:BBRI', 'IDX:BMRI',
            'IDX:TLKM', 'IDX:ASII', 'IDX:UNVR'
        ];

        // Trending topics (placeholder data)
        $trendingTopics = [
            '$BBRI',
            'Danantara',
            'Sri Mulyani',
            '$BBCA',
            'Bank Digital'
        ];

        return view('home', [
            'insights' => $insights,
            'insight_categories' => $insight_categories,
            'mostLikedInsights' => $mostLikedInsights,
            'mostSavedInsights' => $mostSavedInsights,
            'categoryStats' => $categoryStats,
            'stockSymbols' => $stockSymbols,
            'trendingTopics' => $trendingTopics,
        ]);
    }
}
