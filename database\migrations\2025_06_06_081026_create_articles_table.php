<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('articles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users'); // Author/Journalist
            $table->string('title');
            $table->text('content');
            $table->foreignId('category_id')->constrained('categories');
            $table->string('status', 50)->default('draft'); // e.g., 'draft', 'published'
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('articles');
    }
};
