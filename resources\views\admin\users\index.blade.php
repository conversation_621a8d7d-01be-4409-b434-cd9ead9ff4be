@extends('admin.layout')

@section('title', 'User Management - Admin Panel')
@section('page-title', 'User Management')
@section('page-description', 'Manage user accounts and roles')

@section('content')
<div class="space-y-6">
    <!-- Users Table -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">All Users</h3>
                    <div class="text-sm text-gray-500 ml-4">
                        <span id="user-count">{{ $users->total() }}</span> users found
                    </div>
                </div>
                <div class="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
                    <div class="relative">
                        <input type="text"
                               id="search-users"
                               placeholder="Search users..."
                               value="{{ request('search') }}"
                               class="border border-gray-300 rounded-lg px-3 py-2 pr-10 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 w-full sm:w-64">
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                        </div>
                    </div>
                    <select id="role-filter" class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">All Roles</option>
                        @foreach($roles as $role)
                            <option value="{{ $role->id }}" {{ request('role_filter') == $role->id ? 'selected' : '' }}>{{ $role->name }}</option>
                        @endforeach
                    </select>
                    <button id="clear-filters" class="px-3 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                        Clear
                    </button>
                </div>
            </div>
            <div id="loading-indicator" class="hidden mt-2">
                <div class="flex items-center text-sm text-gray-500">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                    Searching...
                </div>
            </div>
        </div>

        <div id="users-table-container">
            @include('admin.users.table', ['users' => $users])
        </div>

        <div id="users-pagination" class="px-6 py-4 border-t border-gray-200">
            @if($users->hasPages())
                {{ $users->links() }}
            @endif
        </div>
    </div>
</div>

<!-- User Details Modal -->
<div id="userModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center pb-3">
            <h3 class="text-lg font-bold text-gray-900">User Details</h3>
            <button onclick="closeUserModal()" class="text-gray-400 hover:text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
        </div>
        <div id="userModalContent">
            <!-- User details will be loaded here -->
        </div>
    </div>
</div>

@if(session('success'))
    <div class="fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg shadow-lg z-50">
        {{ session('success') }}
    </div>
@endif

@if(session('error'))
    <div class="fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg shadow-lg z-50">
        {{ session('error') }}
    </div>
@endif
@endsection

@push('styles')
<style>
    /* Mobile responsive improvements */
    @media (max-width: 640px) {
        .overflow-x-auto {
            -webkit-overflow-scrolling: touch;
        }

        .table-mobile-scroll {
            min-width: 600px;
        }
    }

    /* Loading animation */
    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }

    .loading-pulse {
        animation: pulse 1.5s ease-in-out infinite;
    }
</style>
@endpush

@push('scripts')
<script>
    let searchTimeout;

    // Search and filter functionality
    function performSearch() {
        const search = document.getElementById('search-users').value;
        const roleFilter = document.getElementById('role-filter').value;

        // Show loading indicator
        document.getElementById('loading-indicator').classList.remove('hidden');

        // Build query parameters
        const params = new URLSearchParams();
        if (search) params.append('search', search);
        if (roleFilter) params.append('role_filter', roleFilter);

        // Make AJAX request
        fetch(`{{ route('admin.users') }}?${params.toString()}`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            // Update table content
            document.getElementById('users-table-container').innerHTML = data.html;

            // Update pagination
            document.getElementById('users-pagination').innerHTML = data.pagination;

            // Update count
            document.getElementById('user-count').textContent = data.total;

            // Update URL without page reload
            const newUrl = `{{ route('admin.users') }}${params.toString() ? '?' + params.toString() : ''}`;
            window.history.pushState({}, '', newUrl);
        })
        .catch(error => {
            console.error('Search error:', error);
        })
        .finally(() => {
            // Hide loading indicator
            document.getElementById('loading-indicator').classList.add('hidden');
        });
    }

    // Search input with debouncing
    document.getElementById('search-users').addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(performSearch, 300);
    });

    // Role filter change
    document.getElementById('role-filter').addEventListener('change', performSearch);

    // Clear filters
    document.getElementById('clear-filters').addEventListener('click', function() {
        document.getElementById('search-users').value = '';
        document.getElementById('role-filter').value = '';
        performSearch();
    });

    // Handle pagination clicks
    document.addEventListener('click', function(e) {
        if (e.target.closest('#users-pagination a')) {
            e.preventDefault();
            const url = e.target.closest('a').href;
            const urlParams = new URL(url).searchParams;

            // Add current search and filter parameters
            const search = document.getElementById('search-users').value;
            const roleFilter = document.getElementById('role-filter').value;

            if (search) urlParams.set('search', search);
            if (roleFilter) urlParams.set('role_filter', roleFilter);

            // Show loading
            document.getElementById('loading-indicator').classList.remove('hidden');

            fetch(`{{ route('admin.users') }}?${urlParams.toString()}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('users-table-container').innerHTML = data.html;
                document.getElementById('users-pagination').innerHTML = data.pagination;
                document.getElementById('user-count').textContent = data.total;

                // Update URL
                window.history.pushState({}, '', `{{ route('admin.users') }}?${urlParams.toString()}`);
            })
            .catch(error => console.error('Pagination error:', error))
            .finally(() => {
                document.getElementById('loading-indicator').classList.add('hidden');
            });
        }
    });

    // Email verification functionality
    function verifyUserEmail(userId) {
        // Show SweetAlert2 confirmation dialog
        Swal.fire({
            title: 'Verify Email Address',
            text: 'Are you sure you want to verify this user\'s email address?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#10b981',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'Yes, verify it!',
            cancelButtonText: 'Cancel',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                performEmailVerification(userId);
            }
        });
    }

    // Perform the actual email verification
    function performEmailVerification(userId) {
        const verifyBtn = document.getElementById(`verify-btn-${userId}`);
        const originalText = verifyBtn.textContent;

        // Show loading state
        verifyBtn.textContent = 'Verifying...';
        verifyBtn.disabled = true;
        verifyBtn.classList.add('opacity-50', 'cursor-not-allowed');

        // Make AJAX request
        fetch(`/admin/users/${userId}/verify-email`, {
            method: 'PUT',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update verification status badge
                const statusElement = document.getElementById(`verification-status-${userId}`);
                statusElement.innerHTML = `
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Verified
                    </span>
                `;

                // Remove the verify button
                verifyBtn.remove();

                // Show success notification with SweetAlert2
                showToast('success', data.message);
            } else {
                // Reset button state
                verifyBtn.textContent = originalText;
                verifyBtn.disabled = false;
                verifyBtn.classList.remove('opacity-50', 'cursor-not-allowed');

                // Show error notification with SweetAlert2
                showToast('error', data.message);
            }
        })
        .catch(error => {
            console.error('Email verification error:', error);

            // Reset button state
            verifyBtn.textContent = originalText;
            verifyBtn.disabled = false;
            verifyBtn.classList.remove('opacity-50', 'cursor-not-allowed');

            // Show error notification with SweetAlert2
            showToast('error', 'Failed to verify email. Please try again.');
        });
    }


    // Email verification from modal
    function verifyUserEmailFromModal(userId) {
        // Show SweetAlert2 confirmation dialog
        Swal.fire({
            title: 'Verify Email Address',
            text: 'Are you sure you want to verify this user\'s email address?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#10b981',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'Yes, verify it!',
            cancelButtonText: 'Cancel',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                performModalEmailVerification(userId);
            }
        });
    }

    // Perform the actual email verification from modal
    function performModalEmailVerification(userId) {
        const modalVerifyBtn = document.getElementById(`modal-verify-btn-${userId}`);
        const originalText = modalVerifyBtn.textContent;

        // Show loading state
        modalVerifyBtn.textContent = 'Verifying...';
        modalVerifyBtn.disabled = true;
        modalVerifyBtn.classList.add('opacity-50', 'cursor-not-allowed');

        // Make AJAX request
        fetch(`/admin/users/${userId}/verify-email`, {
            method: 'PUT',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update modal verification status
                const modalStatusElement = document.getElementById(`modal-verification-status-${userId}`);
                modalStatusElement.innerHTML = `
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Verified</span>
                `;

                // Update main table verification status
                const tableStatusElement = document.getElementById(`verification-status-${userId}`);
                if (tableStatusElement) {
                    tableStatusElement.innerHTML = `
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Verified
                        </span>
                    `;
                }

                // Remove verify buttons
                modalVerifyBtn.remove();
                const tableVerifyBtn = document.getElementById(`verify-btn-${userId}`);
                if (tableVerifyBtn) {
                    tableVerifyBtn.remove();
                }

                // Show success notification with SweetAlert2
                showToast('success', data.message);
            } else {
                // Reset button state
                modalVerifyBtn.textContent = originalText;
                modalVerifyBtn.disabled = false;
                modalVerifyBtn.classList.remove('opacity-50', 'cursor-not-allowed');

                // Show error notification with SweetAlert2
                showToast('error', data.message);
            }
        })
        .catch(error => {
            console.error('Email verification error:', error);

            // Reset button state
            modalVerifyBtn.textContent = originalText;
            modalVerifyBtn.disabled = false;
            modalVerifyBtn.classList.remove('opacity-50', 'cursor-not-allowed');

            // Show error notification with SweetAlert2
            showToast('error', 'Failed to verify email. Please try again.');
        });
    }

    function viewUser(userId) {
        // Show loading state
        document.getElementById('userModalContent').innerHTML = `
            <div class="flex items-center justify-center py-12">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span class="ml-3 text-gray-600">Loading user details...</span>
            </div>
        `;
        document.getElementById('userModal').classList.remove('hidden');

        // Fetch user details via AJAX
        fetch(`/admin/users/${userId}/details`)
            .then(response => response.json())
            .then(data => {
                const verificationBadge = data.email_verified ?
                    '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Verified</span>' :
                    '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Unverified</span>';

                document.getElementById('userModalContent').innerHTML = `
                    <div class="space-y-6">
                        <!-- User Header -->
                        <div class="text-center border-b border-gray-200 pb-6">
                            <div class="w-20 h-20 bg-blue-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                                <span class="text-blue-600 font-semibold text-2xl">${data.name.charAt(0)}</span>
                            </div>
                            <h4 class="text-xl font-bold text-gray-900">${data.name}</h4>
                            <p class="text-gray-500">${data.email}</p>
                            <div class="flex items-center justify-center space-x-2 mt-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">${data.role}</span>
                                <span id="modal-verification-status-${data.id}">${verificationBadge}</span>
                            </div>
                            ${!data.email_verified ? `
                                <div class="mt-3">
                                    <button id="modal-verify-btn-${data.id}"
                                            onclick="verifyUserEmailFromModal(${data.id})"
                                            class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm">
                                        Verify Email Address
                                    </button>
                                </div>
                            ` : ''}
                        </div>

                        <!-- User Information -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h5 class="font-medium text-gray-900 mb-3">Account Information</h5>
                                <div class="space-y-2 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-gray-500">User ID:</span>
                                        <span class="text-gray-900">#${data.id}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-500">Joined:</span>
                                        <span class="text-gray-900">${data.created_at}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-500">Last Activity:</span>
                                        <span class="text-gray-900">${data.last_login}</span>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <h5 class="font-medium text-gray-900 mb-3">Content Statistics</h5>
                                <div class="space-y-2 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-gray-500">Total Articles:</span>
                                        <span class="text-gray-900">${data.statistics.total_articles}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-500">Published Articles:</span>
                                        <span class="text-green-600 font-medium">${data.statistics.published_articles}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-500">Total Education:</span>
                                        <span class="text-gray-900">${data.statistics.total_education}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-500">Published Education:</span>
                                        <span class="text-green-600 font-medium">${data.statistics.published_education}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Activity Summary -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h5 class="font-medium text-gray-900 mb-2">Activity Summary</h5>
                            <div class="grid grid-cols-2 gap-4 text-center">
                                <div>
                                    <div class="text-2xl font-bold text-blue-600">${data.statistics.total_articles + data.statistics.total_education}</div>
                                    <div class="text-xs text-gray-500">Total Content</div>
                                </div>
                                <div>
                                    <div class="text-2xl font-bold text-green-600">${data.statistics.published_articles + data.statistics.published_education}</div>
                                    <div class="text-xs text-gray-500">Published Content</div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            })
            .catch(error => {
                console.error('Error fetching user details:', error);
                document.getElementById('userModalContent').innerHTML = `
                    <div class="text-center py-12">
                        <div class="text-red-600 mb-2">
                            <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <p class="text-gray-600">Failed to load user details. Please try again.</p>
                        <button onclick="viewUser(${userId})" class="mt-3 text-blue-600 hover:text-blue-700">Retry</button>
                    </div>
                `;
            });
    }

    // User deletion confirmation with SweetAlert2
    function confirmDeleteUser(userId, userName) {
        Swal.fire({
            title: 'Delete User',
            html: `Are you sure you want to delete <strong>${userName}</strong>?<br><small class="text-gray-500">This action cannot be undone.</small>`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc2626',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'Yes, delete user!',
            cancelButtonText: 'Cancel',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                // Submit the delete form
                document.getElementById(`delete-form-${userId}`).submit();
            }
        });
    }

    function closeUserModal() {
        document.getElementById('userModal').classList.add('hidden');
    }

    // Auto-hide flash messages
    setTimeout(function() {
        $('.fixed.top-4.right-4').fadeOut();
    }, 5000);

    // Close modal when clicking outside
    document.getElementById('userModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeUserModal();
        }
    });
</script>
@endpush
