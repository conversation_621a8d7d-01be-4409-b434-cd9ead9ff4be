<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Article;
use App\Models\Category;
use Illuminate\Support\Facades\Auth;

class ArticleController extends Controller
{
    /**
     * Display a listing of articles and the data needed for the modal form.
     */
    public function index(Request $request)
    {
        // Start with published articles query
        $query = Article::with('user', 'category')
                        ->where('status', 'published');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('category', function($catQuery) use ($search) {
                      $catQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Apply category filter if provided
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        // Get filtered articles
        $articles = $query->latest()->paginate(15)->appends($request->query());

        // For AJAX requests, return JSON
        if ($request->ajax()) {
            return response()->json([
                'html' => view('partials.articles-grid', compact('articles'))->render(),
                'pagination' => $articles->links()->toHtml(),
                'total' => $articles->total()
            ]);
        }

        // Get article categories for filtering
        $categories = Category::where('type', 'article')->get();

        // Get selected category for UI
        $selectedCategory = null;
        if ($request->filled('category')) {
            $selectedCategory = $categories->firstWhere('id', $request->category);
        }

        return view('articles.index', [
            'articles' => $articles,
            'categories' => $categories,
            'selectedCategory' => $selectedCategory
        ]);
    }

    /**
     * Store a newly created article. Returns JSON.
     */
    public function create()
    {
        $categories = Category::where('type', 'article')->get();
        return view('articles.create', compact('categories'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'category_id' => 'required|exists:categories,id',
            'image_url' => 'nullable|url'
        ]);

        Article::create([
            'user_id' => Auth::id(),
            'title' => $request->title,
            'content' => $request->content,
            'category_id' => $request->category_id,
            'image_url' => $request->image_url,
            'status' => 'pending' // Always starts as pending for admin approval
        ]);

        return redirect()->route('articles.my-articles')
                        ->with('success', 'Article submitted for review. It will be published after admin approval.');
    }

    public function myArticles()
    {
        $articles = Article::with(['category'])
                          ->where('user_id', Auth::id())
                          ->latest()
                          ->paginate(10);

        return view('articles.my-articles', compact('articles'));
    }

    /**
     * Update the specified article. Returns JSON.
     */
    public function update(Request $request, Article $article)
    {
        // Only author can update their own articles, and only if pending
        if ($article->user_id !== Auth::id() || $article->status !== 'pending') {
            abort(403, 'You can only edit your own pending articles.');
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'category_id' => 'required|exists:categories,id',
            'image_url' => 'nullable|url'
        ]);

        $article->update([
            'title' => $request->title,
            'content' => $request->content,
            'category_id' => $request->category_id,
            'image_url' => $request->image_url,
        ]);

        return redirect()->route('articles.my-articles')
                        ->with('success', 'Article updated successfully.');
    }

    /**
     * Display the specified article (this still needs its own view).
     */
    public function show(Article $article)
    {
        // Check if user can view this article
        if ($article->status !== 'published' &&
            (!Auth::check() || (Auth::user()->id !== $article->user_id && Auth::user()->role->name !== 'Admin'))) {
            abort(404);
        }

        return view('articles.show', compact('article'));
    }

    public function edit(Article $article)
    {
        // Only author can edit their own articles, and only if pending
        if ($article->user_id !== Auth::id() || $article->status !== 'pending') {
            abort(403, 'You can only edit your own pending articles.');
        }

        $categories = Category::where('type', 'article')->get();
        return view('articles.edit', compact('article', 'categories'));
    }

    /**
     * Remove the specified article from storage.
     */
    public function destroy(Article $article)
    {
        // Only author can delete their own articles, and only if pending
        if ($article->user_id !== Auth::id() || $article->status !== 'pending') {
            abort(403, 'You can only delete your own pending articles.');
        }

        $article->delete();

        return redirect()->route('articles.my-articles')
                        ->with('success', 'Article deleted successfully.');
    }
}
