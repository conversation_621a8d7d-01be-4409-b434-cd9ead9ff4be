<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Education extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'title',
        'content',
        'category_id',
        'image_url',
        'video_url',
        'status'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    // Scope for published education content
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    // Scope for pending education content
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function saves()
    {
        return $this->hasMany(EducationSave::class);
    }

    public function savedBy()
    {
        return $this->belongsToMany(User::class, 'education_saves');
    }

    // Check if education content is saved by a specific user
    public function isSavedBy($userId)
    {
        return $this->saves()->where('user_id', $userId)->exists();
    }
}
