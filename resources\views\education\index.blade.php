@extends('layout')

@section('title', 'Education - InsightFlow')

@section('content')
<div class="p-4 lg:p-8">
    <div class="max-w-7xl mx-auto">
        <!-- Header Section -->
        <div class="mb-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div>
                    <h1 class="text-2xl lg:text-3xl font-bold text-gray-800">Financial Education</h1>
                    <p class="text-gray-600 mt-2">
                        @if($selectedCategory)
                            Showing education content in <span class="font-medium text-blue-600">{{ $selectedCategory->name }}</span> category
                        @else
                            Learn about investing, trading, and financial markets
                        @endif
                    </p>
                </div>
                <div class="flex items-center gap-3">
                    <x-dropdown
                        trigger="{{ $selectedCategory ? $selectedCategory->name : 'All Categories' }}"
                        :icon="true"
                        button-class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white hover:bg-gray-50 text-gray-700 {{ $selectedCategory ? 'border-blue-500 bg-blue-50' : '' }}"
                    >
                        <x-dropdown-item href="?category=" onclick="filterByCategory('', 'All Categories')">
                            All Categories
                        </x-dropdown-item>
                        @foreach($categories as $category)
                            <x-dropdown-item href="?category={{ $category->id }}" onclick="filterByCategory('{{ $category->id }}', '{{ $category->name }}')">
                                {{ $category->name }}
                            </x-dropdown-item>
                        @endforeach
                    </x-dropdown>

                    @if($selectedCategory)
                        <a href="{{ route('education.index') }}"
                           class="text-gray-500 hover:text-gray-700 text-sm flex items-center gap-1 transition-colors"
                           title="Clear filter">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                            Clear filter
                        </a>
                    @endif

                    @auth
                        <a href="{{ route('education.create') }}" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm lg:text-base">
                            Create Content
                        </a>
                    @endauth
                </div>
            </div>
        </div>

        <!-- Education Content -->
        <div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @forelse($educations as $education)
                <div class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                    <div class="relative">
                        @if($education->image_url)
                            <img src="{{ $education->image_url }}"
                                 alt="{{ $education->title }}"
                                 class="w-full h-48 object-cover"
                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="w-full h-48 bg-gradient-to-br from-blue-400 to-blue-500 flex items-center justify-center" style="display: none;">
                                <span class="text-white text-2xl font-bold">{{ substr($education->title, 0, 1) }}</span>
                            </div>
                        @else
                            <div class="w-full h-48 bg-gradient-to-br from-blue-400 to-blue-500 flex items-center justify-center">
                                <span class="text-white text-2xl font-bold">{{ substr($education->title, 0, 1) }}</span>
                            </div>
                        @endif

                        @if($education->video_url)
                            <div class="absolute top-3 right-3 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs flex items-center">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M2 6a2 2 0 012-2h6l2 2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"/>
                                </svg>
                                Video
                            </div>
                        @endif
                    </div>

                    <div class="p-6">
                        <div class="flex items-center mb-3">
                            <span class="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">{{ $education->category->name }}</span>
                            <span class="text-xs text-gray-500 ml-auto">{{ $education->created_at->format('M d, Y') }}</span>
                        </div>

                        <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">{{ $education->title }}</h3>
                        <p class="text-gray-600 text-sm mb-4 line-clamp-3">{{ Str::limit(strip_tags($education->content), 120) }}</p>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-6 h-6 rounded-full bg-blue-100 mr-2 flex items-center justify-center">
                                    <span class="text-blue-600 font-semibold text-xs">{{ substr($education->user->name, 0, 1) }}</span>
                                </div>
                                <span class="text-sm text-gray-500">{{ $education->user->name }}</span>
                            </div>

                            <div class="flex items-center space-x-2">
                                @auth
                                    <button onclick="toggleEducationSave({{ $education->id }})"
                                            class="education-save-btn text-gray-500 hover:text-blue-500 transition-colors {{ $education->isSavedBy(auth()->id()) ? 'text-blue-500' : '' }}"
                                            data-education-id="{{ $education->id }}">
                                        <svg class="w-4 h-4" fill="{{ $education->isSavedBy(auth()->id()) ? 'currentColor' : 'none' }}" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"/>
                                        </svg>
                                    </button>
                                @endauth
                                <a href="{{ route('education.show', $education) }}" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                                    Read More →
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @empty
                <div class="col-span-full bg-white rounded-lg shadow-sm p-12 text-center">
                    <div class="w-16 h-16 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No educational content yet</h3>
                    <p class="text-gray-500">Check back later for new educational content</p>
                </div>
            @endforelse
        </div>

            @if($educations->hasPages())
                <div class="mt-8">
                    {{ $educations->appends(request()->query())->links() }}
                </div>
            @endif
        </div>
    </div>
</div>

@push('scripts')
<script>
// Education Save functionality
function toggleEducationSave(educationId) {
    $.post(`/education/${educationId}/save`)
        .done(function(data) {
            const btn = $(`.education-save-btn[data-education-id="${educationId}"]`);
            const icon = btn.find('svg');

            if (data.saved) {
                btn.addClass('text-blue-500').removeClass('text-gray-500');
                icon.attr('fill', 'currentColor');
            } else {
                btn.addClass('text-gray-500').removeClass('text-blue-500');
                icon.attr('fill', 'none');
            }

            // Show SweetAlert2 toast
            showToast(data.saved ? 'success' : 'info', data.message);
        })
        .fail(function() {
            showToast('error', 'Error occurred while saving the education content');
        });
}
</script>
@endpush

@push('scripts')
<script>
function filterByCategory(categoryId, categoryName) {
    // Update the dropdown trigger text immediately for better UX
    const trigger = document.querySelector('[x-ref="button"] span');
    if (trigger) {
        trigger.textContent = categoryName || 'All Categories';
    }

    // Update the URL and navigate
    const url = new URL(window.location);
    if (categoryId) {
        url.searchParams.set('category', categoryId);
    } else {
        url.searchParams.delete('category');
    }
    window.location.href = url.toString();
}
</script>
@endpush

@endsection
