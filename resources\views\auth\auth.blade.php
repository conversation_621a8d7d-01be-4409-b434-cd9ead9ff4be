<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Auth - InsightFlow')</title>

    <!-- Favicon and App Icons -->
    <link rel="icon" type="image/png" href="/assets/logo.png">
    <link rel="apple-touch-icon" href="/assets/logo.png">
    <meta name="theme-color" content="#3B82F6">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #3b82f6;
            --primary-dark: #1d4ed8;
            --secondary-color: #f8fafc;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --border-color: #e5e7eb;
            --success-color: #10b981;
            --error-color: #ef4444;
            --warning-color: #f59e0b;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
        }

        .auth-container {
            background: white;
            min-height: 100vh;
            position: relative;
            overflow: hidden;
        }

        .auth-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('/assets/BG.png') center center;
            background-size: cover;
        }

        .glass-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .form-container {
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            transform-origin: center;
        }

        .form-slide-in {
            animation: slideIn 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }

        .form-slide-out {
            animation: slideOut 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }

        @keyframes slideIn {
            from {
                transform: scale(0.95);
            }
            to {
                transform: scale(1);
            }
        }

        @keyframes slideOut {
            from {
                transform: scale(1);
            }
            to {
                transform: scale(0.95);
            }
        }

        .input-group {
            position: relative;
            margin-bottom: 1.5rem;
        }

        .input-wrapper {
            position: relative;
            display: block;
            width: 100%;
        }

        .input-field {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #ffffff;
            color: var(--text-primary);
        }

        .input-field:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            transform: translateY(-1px);
        }

        .input-field.error {
            border-color: var(--error-color);
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .input-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            transition: color 0.3s ease;
            z-index: 2;
        }

        .input-field:focus + .input-icon {
            color: var(--primary-color);
        }

        .password-toggle {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: var(--text-secondary);
            transition: color 0.3s ease;
            z-index: 2;
        }

        .password-toggle:hover {
            color: var(--primary-color);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            width: 100%;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
        }

        .btn-primary:active {
            transform: translateY(0);
        }

        .btn-primary:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .btn-loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .toggle-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            position: relative;
            transition: color 0.3s ease;
        }

        .toggle-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -2px;
            left: 50%;
            background-color: var(--primary-color);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .toggle-link:hover::after {
            width: 100%;
        }

        .strength-meter {
            height: 4px;
            background: #e5e7eb;
            border-radius: 2px;
            margin-top: 0.5rem;
            overflow: hidden;
        }

        .strength-fill {
            height: 100%;
            border-radius: 2px;
            transition: all 0.3s ease;
            width: 0%;
        }

        .strength-weak { background: var(--error-color); }
        .strength-fair { background: var(--warning-color); }
        .strength-good { background: var(--primary-color); }
        .strength-strong { background: var(--success-color); }

        .error-message {
            color: var(--error-color);
            font-size: 0.875rem;
            margin-top: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .success-message {
            color: var(--success-color);
            font-size: 0.875rem;
            margin-top: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .fade-in {
            animation: fadeIn 0.5s ease forwards;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .auth-container {
                padding: 1rem;
            }
            
            .glass-card {
                margin: 1rem;
            }
            
            .form-container {
                padding: 2rem 1.5rem;
            }
            
            .register-grid {
                grid-template-columns: 1fr !important;
            }
        }

        /* Loading States */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .loading-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-dark);
        }
    </style>
</head>
<body>
    <div class="auth-container flex items-center justify-center min-h-screen">
        <div class="w-full max-w-md mx-4 relative z-10">
            <!-- Login Form Container -->
            <div id="login-container" class="glass-card rounded-2xl p-8 form-container">
                <div class="text-center mb-8">
                    <h1 class="text-3xl font-bold text-gray-800 mb-2">Welcome Back</h1>
                    <p class="text-gray-600">Sign in to continue to InsightFlow</p>
                </div>

                <form id="login-form" action="{{ route('login') }}" method="POST" novalidate>
                    @csrf
                    <div class="input-group">
                        <div class="input-wrapper">
                            <input type="email"
                                   id="login-email"
                                   name="email"
                                   class="input-field"
                                   placeholder="Enter your email address"
                                   value="{{ old('email') }}"
                                   required
                                   autocomplete="email">
                            <i class="fas fa-envelope input-icon"></i>
                        </div>
                        <div class="error-message" id="login-email-error" style="display: none;">
                            <i class="fas fa-exclamation-circle"></i>
                            <span></span>
                        </div>
                    </div>

                    <div class="input-group">
                        <div class="input-wrapper">
                            <input type="password"
                                   id="login-password"
                                   name="password"
                                   class="input-field"
                                   placeholder="Enter your password"
                                   required
                                   autocomplete="current-password">
                            <i class="fas fa-lock input-icon"></i>
                            <i class="fas fa-eye password-toggle" data-target="login-password"></i>
                        </div>
                        <div class="error-message" id="login-password-error" style="display: none;">
                            <i class="fas fa-exclamation-circle"></i>
                            <span></span>
                        </div>
                    </div>

                    <button type="submit" class="btn-primary mb-6">
                        <span class="btn-text">Sign In</span>
                    </button>

                    <div class="text-center">
                        <p class="text-gray-600 text-sm">
                            Don't have an account? 
                            <a href="#" class="toggle-link" id="show-register">Create one here</a>
                        </p>
                    </div>
                </form>
            </div>

            <!-- Register Form Container -->
            <div id="register-container" class="glass-card rounded-2xl p-8 form-container" style="display: none;">
                <div class="text-center mb-8">
                    <h1 class="text-3xl font-bold text-gray-800 mb-2">Create Account</h1>
                    <p class="text-gray-600">Join our community today</p>
                </div>

                <form id="register-form" action="{{ route('register') }}" method="POST" novalidate>
                    @csrf
                    <div class="input-group">
                        <div class="input-wrapper">
                            <input type="text"
                                   id="register-name"
                                   name="name"
                                   class="input-field"
                                   placeholder="Enter your full name"
                                   value="{{ old('name') }}"
                                   required
                                   autocomplete="name">
                            <i class="fas fa-user input-icon"></i>
                        </div>
                        <div class="error-message" id="register-name-error" style="display: none;">
                            <i class="fas fa-exclamation-circle"></i>
                            <span></span>
                        </div>
                    </div>

                    <div class="input-group">
                        <div class="input-wrapper">
                            <input type="email"
                                   id="register-email"
                                   name="email"
                                   class="input-field"
                                   placeholder="Enter your email address"
                                   value="{{ old('email') }}"
                                   required
                                   autocomplete="email">
                            <i class="fas fa-envelope input-icon"></i>
                        </div>
                        <div class="error-message" id="register-email-error" style="display: none;">
                            <i class="fas fa-exclamation-circle"></i>
                            <span></span>
                        </div>
                    </div>

                    <div class="input-group">
                        <div class="input-wrapper">
                            <input type="password"
                                   id="register-password"
                                   name="password"
                                   class="input-field"
                                   placeholder="Create a secure password"
                                   required
                                   autocomplete="new-password">
                            <i class="fas fa-lock input-icon"></i>
                            <i class="fas fa-eye password-toggle" data-target="register-password"></i>
                        </div>
                        <div class="strength-meter">
                            <div class="strength-fill" id="password-strength"></div>
                        </div>
                        <div class="error-message" id="register-password-error" style="display: none;">
                            <i class="fas fa-exclamation-circle"></i>
                            <span></span>
                        </div>
                    </div>

                    <div class="input-group">
                        <div class="input-wrapper">
                            <input type="password"
                                   id="register-password-confirmation"
                                   name="password_confirmation"
                                   class="input-field"
                                   placeholder="Confirm your password"
                                   required
                                   autocomplete="new-password">
                            <i class="fas fa-lock input-icon"></i>
                            <i class="fas fa-eye password-toggle" data-target="register-password-confirmation"></i>
                        </div>
                        <div class="error-message" id="password-match-error" style="display: none;">
                            <i class="fas fa-exclamation-circle"></i>
                            <span>Passwords don't match</span>
                        </div>
                        <div class="success-message" id="password-match-success" style="display: none;">
                            <i class="fas fa-check-circle"></i>
                            <span>Passwords match</span>
                        </div>
                    </div>

                    <button type="submit" class="btn-primary mb-6">
                        <span class="btn-text">Create Account</span>
                    </button>

                    <div class="text-center">
                        <p class="text-gray-600 text-sm">
                            Already have an account? 
                            <a href="#" class="toggle-link" id="show-login">Sign in here</a>
                        </p>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="bg-white rounded-lg p-6 text-center">
            <div class="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p class="text-gray-600">Processing...</p>
        </div>
    </div>

    <script>
        // Global SweetAlert2 Toast Configuration
        const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 4000,
            timerProgressBar: true,
            customClass: {
                popup: 'colored-toast'
            },
            didOpen: (toast) => {
                toast.addEventListener('mouseenter', Swal.stopTimer)
                toast.addEventListener('mouseleave', Swal.resumeTimer)
            }
        });

        // Global toast function
        window.showToast = function(type, title, message = '') {
            const config = { icon: type, title: title };
            if (message) config.text = message;
            Toast.fire(config);
        };

        // CSRF token setup
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        $(document).ready(function() {
            const loginContainer = $('#login-container');
            const registerContainer = $('#register-container');
            let isAnimating = false;

            // Form switching functionality
            function showLogin() {
                if (isAnimating) return;
                isAnimating = true;

                registerContainer.addClass('form-slide-out');
                setTimeout(() => {
                    registerContainer.hide().removeClass('form-slide-out');
                    loginContainer.show().addClass('form-slide-in');
                    window.history.replaceState(null, null, '#login');
                    isAnimating = false;
                }, 300);
            }

            function showRegister() {
                if (isAnimating) return;
                isAnimating = true;

                loginContainer.addClass('form-slide-out');
                setTimeout(() => {
                    loginContainer.hide().removeClass('form-slide-out');
                    registerContainer.show().addClass('form-slide-in');
                    window.history.replaceState(null, null, '#register');
                    isAnimating = false;
                }, 300);
            }

            $('#show-register').click(function(e) {
                e.preventDefault();
                showRegister();
            });

            $('#show-login').click(function(e) {
                e.preventDefault();
                showLogin();
            });

            // Password toggle functionality
            $('.password-toggle').click(function() {
                const targetId = $(this).data('target');
                const passwordField = $('#' + targetId);
                const isPassword = passwordField.attr('type') === 'password';
                
                passwordField.attr('type', isPassword ? 'text' : 'password');
                $(this).removeClass(isPassword ? 'fa-eye' : 'fa-eye-slash')
                       .addClass(isPassword ? 'fa-eye-slash' : 'fa-eye');
            });

            // Password strength meter
            $('#register-password').on('input', function() {
                const password = $(this).val();
                const strengthFill = $('#password-strength');
                
                let strength = 0;
                let strengthClass = '';
                
                if (password.length >= 8) strength++;
                if (/[A-Z]/.test(password)) strength++;
                if (/[a-z]/.test(password)) strength++;
                if (/[0-9]/.test(password)) strength++;
                if (/[^A-Za-z0-9]/.test(password)) strength++;
                
                const percentage = (strength / 5) * 100;
                
                if (percentage <= 20) strengthClass = 'strength-weak';
                else if (percentage <= 40) strengthClass = 'strength-fair';
                else if (percentage <= 80) strengthClass = 'strength-good';
                else strengthClass = 'strength-strong';
                
                strengthFill.css('width', percentage + '%')
                           .removeClass('strength-weak strength-fair strength-good strength-strong')
                           .addClass(strengthClass);
            });

            // Password confirmation validation
            $('#register-password-confirmation').on('input', function() {
                const password = $('#register-password').val();
                const confirmation = $(this).val();
                const errorMsg = $('#password-match-error');
                const successMsg = $('#password-match-success');
                
                if (confirmation.length > 0) {
                    if (password === confirmation) {
                        $(this).removeClass('error');
                        errorMsg.hide();
                        successMsg.show();
                    } else {
                        $(this).addClass('error');
                        errorMsg.show();
                        successMsg.hide();
                    }
                } else {
                    $(this).removeClass('error');
                    errorMsg.hide();
                    successMsg.hide();
                }
            });

            // Real-time validation
            function validateField(field, rules) {
                const value = field.val().trim();
                const fieldId = field.attr('id');
                const errorContainer = $(`#${fieldId}-error`);
                let isValid = true;
                let errorMessage = '';

                for (let rule of rules) {
                    if (!rule.test(value)) {
                        isValid = false;
                        errorMessage = rule.message;
                        break;
                    }
                }

                if (isValid) {
                    field.removeClass('error');
                    errorContainer.hide();
                } else {
                    field.addClass('error');
                    errorContainer.find('span').text(errorMessage);
                    errorContainer.show();
                }

                return isValid;
            }

            // Validation rules
            const emailRules = [
                { test: val => val.length > 0, message: 'Email is required' },
                { test: val => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val), message: 'Please enter a valid email address' }
            ];

            const passwordRules = [
                { test: val => val.length > 0, message: 'Password is required' },
                { test: val => val.length >= 8, message: 'Password must be at least 8 characters long' }
            ];

            const nameRules = [
                { test: val => val.length > 0, message: 'Name is required' },
                { test: val => val.length >= 2, message: 'Name must be at least 2 characters long' }
            ];

            // Bind validation events
            $('#login-email, #register-email').on('blur', function() {
                validateField($(this), emailRules);
            });

            $('#login-password, #register-password').on('blur', function() {
                validateField($(this), passwordRules);
            });

            $('#register-name').on('blur', function() {
                validateField($(this), nameRules);
            });

            // Form submission handling
            function handleFormSubmission(form, isLogin = true) {
                const submitBtn = form.find('button[type="submit"]');
                const btnText = submitBtn.find('.btn-text');
                const originalText = btnText.text();

                // Show loading state
                submitBtn.prop('disabled', true).addClass('btn-loading');
                btnText.text('Processing...');
                $('#loading-overlay').addClass('active');

                // Validate form before submission
                let isValid = true;
                
                if (isLogin) {
                    isValid = validateField($('#login-email'), emailRules) && 
                             validateField($('#login-password'), passwordRules);
                } else {
                    const passwordMatch = $('#register-password').val() === $('#register-password-confirmation').val();
                    
                    isValid = validateField($('#register-name'), nameRules) &&
                             validateField($('#register-email'), emailRules) &&
                             validateField($('#register-password'), passwordRules) &&
                             passwordMatch && termsAccepted;
                    
                    if (!passwordMatch) {
                        $('#register-password-confirmation').addClass('error');
                        $('#password-match-error').show();
                        isValid = false;
                    }
                    
                    if (!termsAccepted) {
                        showToast('error', 'Please accept the terms and conditions');
                        isValid = false;
                    }
                }

                if (!isValid) {
                    // Reset button state
                    submitBtn.prop('disabled', false).removeClass('btn-loading');
                    btnText.text(originalText);
                    $('#loading-overlay').removeClass('active');
                    return false;
                }

                // Reset after timeout as fallback
                setTimeout(() => {
                    submitBtn.prop('disabled', false).removeClass('btn-loading');
                    btnText.text(originalText);
                    $('#loading-overlay').removeClass('active');
                }, 10000);

                return true;
            }

            $('#login-form').on('submit', function(e) {
                if (!handleFormSubmission($(this), true)) {
                    e.preventDefault();
                }
            });

            $('#register-form').on('submit', function(e) {
                if (!handleFormSubmission($(this), false)) {
                    e.preventDefault();
                }
            });

            // Initialize based on URL
            const hash = window.location.hash;
            if (hash === '#register') {
                showRegister();
            }

            // Handle browser navigation
            window.addEventListener('hashchange', function() {
                if (window.location.hash === '#register') {
                    showRegister();
                } else {
                    showLogin();
                }
            });
        });

        // Session flash messages
        @if(session('success'))
            document.addEventListener('DOMContentLoaded', function() {
                showToast('success', '{{ session('success') }}');
            });
        @endif

        @if(session('error'))
            document.addEventListener('DOMContentLoaded', function() {
                showToast('error', '{{ session('error') }}');
            });
        @endif

        @if($errors->any())
            document.addEventListener('DOMContentLoaded', function() {
                @foreach($errors->all() as $error)
                    showToast('error', 'Validation Error', '{{ $error }}');
                @endforeach
            });
        @endif
    </script>

    <!-- Custom Toast Styles -->
    <style>
        .colored-toast.swal2-icon-success {
            background-color: #a7f3d0 !important;
        }
        .colored-toast.swal2-icon-error {
            background-color: #fecaca !important;
        }
        .colored-toast.swal2-icon-warning {
            background-color: #fde68a !important;
        }
        .colored-toast.swal2-icon-info {
            background-color: #bfdbfe !important;
        }
        .colored-toast .swal2-title {
            color: #374151 !important;
            font-weight: 600 !important;
        }
        .colored-toast .swal2-content {
            color: #6b7280 !important;
        }
    </style>
</body>
</html>