<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Category;

class CategorySeeder extends Seeder
{
    public function run(): void
    {
        // Insight Categories
        Category::create(['name' => 'Stock Analysis', 'type' => 'insight']);
        Category::create(['name' => 'Technical Analysis', 'type' => 'insight']);
        Category::create(['name' => 'Fundamental Analysis', 'type' => 'insight']);
        Category::create(['name' => 'Market Outlook', 'type' => 'insight']);
        Category::create(['name' => 'Sector Analysis', 'type' => 'insight']);

        // Article Categories
        Category::create(['name' => 'Market News', 'type' => 'article']);
        Category::create(['name' => 'Economic Updates', 'type' => 'article']);
        Category::create(['name' => 'Company News', 'type' => 'article']);
        Category::create(['name' => 'Global Markets', 'type' => 'article']);
        Category::create(['name' => 'Cryptocurrency', 'type' => 'article']);
        Category::create(['name' => 'Commodities', 'type' => 'article']);

        // Education Categories
        Category::create(['name' => 'Beginner Investing', 'type' => 'education']);
        Category::create(['name' => 'Stock Market Basics', 'type' => 'education']);
        Category::create(['name' => 'Trading Fundamentals', 'type' => 'education']);
        Category::create(['name' => 'Financial Planning', 'type' => 'education']);
        Category::create(['name' => 'Investment Strategies', 'type' => 'education']);
        Category::create(['name' => 'Risk Assessment', 'type' => 'education']);
    }
}
