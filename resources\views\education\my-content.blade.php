@extends('layout')

@section('title', 'My Education Content - InsightFlow')

@section('content')
<div class="p-8">
    <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">My Education Content</h1>
                    <p class="text-gray-600 mt-2">Manage your educational articles and content</p>
                </div>
                <a href="{{ route('education.create') }}" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    Create New Content
                </a>
            </div>
        </div>

        @if(session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg mb-6">
                {{ session('success') }}
            </div>
        @endif

        <!-- Content List -->
        <div class="bg-white rounded-lg shadow-sm">
            @forelse($educations as $education)
                <div class="border-b border-gray-200 last:border-b-0 p-6">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <div class="flex items-center gap-3 mb-2">
                                <h3 class="text-lg font-semibold text-gray-900">{{ $education->title }}</h3>
                                @if($education->video_url)
                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 flex items-center">
                                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M2 6a2 2 0 012-2h6l2 2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"/>
                                        </svg>
                                        Video
                                    </span>
                                @endif
                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                    {{ $education->status === 'published' ? 'bg-green-100 text-green-800' :
                                       ($education->status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                                    {{ ucfirst($education->status) }}
                                </span>
                            </div>

                            <div class="flex items-center text-sm text-gray-500 mb-3">
                                <span class="bg-gray-100 px-2 py-1 rounded text-xs mr-3">{{ $education->category->name }}</span>
                                <span>Created {{ $education->created_at->diffForHumans() }}</span>
                                @if($education->updated_at != $education->created_at)
                                    <span class="mx-2">•</span>
                                    <span>Updated {{ $education->updated_at->diffForHumans() }}</span>
                                @endif
                            </div>

                            <p class="text-gray-600 text-sm leading-relaxed">
                                {{ Str::limit(strip_tags($education->content), 200) }}
                            </p>
                        </div>

                        <div class="ml-6 flex items-center space-x-2">
                            @if($education->status === 'published')
                                <a href="{{ route('education.show', $education) }}"
                                   class="text-green-600 hover:text-green-700 text-sm font-medium">
                                    View
                                </a>
                            @endif

                            @if($education->status === 'pending')
                                <a href="{{ route('education.edit', $education) }}"
                                   class="text-indigo-600 hover:text-indigo-700 text-sm font-medium">
                                    Edit
                                </a>

                                <form action="{{ route('education.destroy', $education) }}"
                                      method="POST"
                                      class="inline"
                                      onsubmit="return confirm('Are you sure you want to delete this content?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="text-red-600 hover:text-red-700 text-sm font-medium">
                                        Delete
                                    </button>
                                </form>
                            @endif
                        </div>
                    </div>

                    @if($education->status === 'pending')
                        <div class="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                            <div class="flex items-center">
                                <svg class="w-4 h-4 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                                </svg>
                                <span class="text-yellow-800 text-sm">Waiting for admin approval</span>
                            </div>
                        </div>
                    @elseif($education->status === 'rejected')
                        <div class="mt-4 bg-red-50 border border-red-200 rounded-lg p-3">
                            <div class="flex items-center">
                                <svg class="w-4 h-4 text-red-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                                </svg>
                                <span class="text-red-800 text-sm">Content was rejected by admin</span>
                            </div>
                        </div>
                    @elseif($education->status === 'published')
                        <div class="mt-4 bg-green-50 border border-green-200 rounded-lg p-3">
                            <div class="flex items-center">
                                <svg class="w-4 h-4 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                </svg>
                                <span class="text-green-800 text-sm">Published and visible to all users</span>
                            </div>
                        </div>
                    @endif
                </div>
            @empty
                <div class="p-12 text-center">
                    <div class="w-16 h-16 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No education content yet</h3>
                    <p class="text-gray-500 mb-4">Start sharing your knowledge with the community</p>
                    <a href="{{ route('education.create') }}" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                        </svg>
                        Create Your First Content
                    </a>
                </div>
            @endforelse
        </div>

        <!-- Pagination -->
        @if($educations->hasPages())
            <div class="mt-8">
                {{ $educations->links() }}
            </div>
        @endif
    </div>
</div>
@endsection
