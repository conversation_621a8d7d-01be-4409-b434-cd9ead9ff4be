@props([
    'href' => '#',
    'icon' => null,
    'danger' => false,
    'onclick' => null
])

@php
$classes = $danger 
    ? 'px-2 lg:py-1.5 py-2 w-full flex items-center rounded-md transition-colors text-left text-gray-800 hover:bg-red-50 hover:text-red-600 focus-visible:bg-red-50 focus-visible:text-red-600 disabled:opacity-50 disabled:cursor-not-allowed'
    : 'px-2 lg:py-1.5 py-2 w-full flex items-center rounded-md transition-colors text-left text-gray-800 hover:bg-gray-50 focus-visible:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed';
@endphp

@if($onclick)
    <button 
        type="button"
        onclick="{{ $onclick }}"
        class="{{ $classes }}"
        {{ $attributes }}
    >
        @if($icon)
            <i class="{{ $icon }} mr-2"></i>
        @endif
        {{ $slot }}
    </button>
@else
    <a 
        href="{{ $href }}" 
        class="{{ $classes }}"
        {{ $attributes }}
    >
        @if($icon)
            <i class="{{ $icon }} mr-2"></i>
        @endif
        {{ $slot }}
    </a>
@endif
