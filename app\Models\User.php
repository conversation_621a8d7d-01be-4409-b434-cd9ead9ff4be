<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role_id',
        'email_verified_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    // Relationships
    public function role()
    {
        return $this->belongsTo(Role::class);
    }

    public function insights()
    {
        return $this->hasMany(Insight::class);
    }

    public function articles()
    {
        return $this->hasMany(Article::class);
    }

    public function comments()
    {
        return $this->hasMany(Comment::class);
    }

    public function insightLikes()
    {
        return $this->hasMany(InsightLike::class);
    }

    public function insightSaves()
    {
        return $this->hasMany(InsightSave::class);
    }

    public function savedInsights()
    {
        return $this->belongsToMany(Insight::class, 'insight_saves');
    }

    public function articleSaves()
    {
        return $this->hasMany(ArticleSave::class);
    }

    public function savedArticles()
    {
        return $this->belongsToMany(Article::class, 'article_saves');
    }

    public function educationSaves()
    {
        return $this->hasMany(EducationSave::class);
    }

    public function savedEducation()
    {
        return $this->belongsToMany(Education::class, 'education_saves');
    }
}
