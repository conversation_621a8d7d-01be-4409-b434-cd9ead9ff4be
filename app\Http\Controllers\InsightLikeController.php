<?php

namespace App\Http\Controllers;

use App\Models\Insight;
use App\Models\InsightLike;
use Illuminate\Support\Facades\Auth;

class InsightLikeController extends Controller
{
    public function toggle(Insight $insight)
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'You must be logged in to like insights'
                ], 401);
            }

            $existingLike = InsightLike::where('user_id', $user->id)
                                      ->where('insight_id', $insight->id)
                                      ->first();

            if ($existingLike) {
                // Unlike
                $existingLike->delete();
                $insight->decrement('likes_count');
                $liked = false;
                $message = 'Like removed';
            } else {
                // Like
                InsightLike::create([
                    'user_id' => $user->id,
                    'insight_id' => $insight->id,
                ]);
                $insight->increment('likes_count');
                $liked = true;
                $message = 'Insight liked!';
            }

            // Get fresh insight data
            $freshInsight = $insight->fresh();

            // Get updated most liked insights for sidebar
            $mostLikedInsights = Insight::with(['user', 'category'])
                                       ->where('status', 'published')
                                       ->mostLiked(5)
                                       ->get();

            return response()->json([
                'success' => true,
                'liked' => $liked,
                'likes_count' => $freshInsight->likes_count,
                'message' => $message,
                'most_liked_html' => view('partials.most-liked-insights', compact('mostLikedInsights'))->render()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing your request'
            ], 500);
        }
    }

    /**
     * Get most liked insights for AJAX requests
     */
    public function getMostLiked()
    {
        try {
            $mostLikedInsights = Insight::with(['user', 'category'])
                                       ->where('status', 'published')
                                       ->mostLiked(5)
                                       ->get();

            return response()->json([
                'success' => true,
                'html' => view('partials.most-liked-insights', compact('mostLikedInsights'))->render()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load most liked insights'
            ], 500);
        }
    }
}
