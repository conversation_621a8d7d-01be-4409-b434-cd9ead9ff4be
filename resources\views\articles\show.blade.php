@extends('layout')

@section('title', $article->title . ' - InsightFlow')

@section('content')
<div class="p-8">
    <div class="max-w-4xl mx-auto">
        <!-- Breadcrumb -->
        <nav class="mb-6">
            <ol class="flex items-center space-x-2 text-sm text-gray-500">
                <li><a href="{{ route('home') }}" class="hover:text-gray-700">Home</a></li>
                <li>/</li>
                <li><a href="{{ route('articles.index') }}" class="hover:text-gray-700">Articles</a></li>
                <li>/</li>
                <li class="text-gray-900">{{ Str::limit($article->title, 50) }}</li>
            </ol>
        </nav>

        <!-- Article Header -->
        <div class="bg-white rounded-lg shadow-sm p-8 mb-6">
            <div class="mb-4">
                <span class="text-sm text-blue-600 bg-blue-100 px-3 py-1 rounded">{{ $article->category->name }}</span>
            </div>

            <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ $article->title }}</h1>

            <div class="flex items-center justify-between text-sm text-gray-500 mb-6">
                <div class="flex items-center">
                    <div class="w-8 h-8 rounded-full bg-blue-100 mr-3 flex items-center justify-center">
                        <span class="text-blue-600 font-semibold text-sm">{{ substr($article->user->name, 0, 1) }}</span>
                    </div>
                    <div>
                        <p class="font-medium text-gray-900">{{ $article->user->name }}</p>
                        <p>{{ $article->created_at->format('F j, Y') }} • {{ $article->created_at->diffForHumans() }}</p>
                    </div>
                </div>

                <div class="flex items-center space-x-4">
                    @auth
                        <button onclick="toggleArticleSave({{ $article->id }})"
                                class="article-save-btn flex items-center space-x-1 text-gray-500 hover:text-blue-600 transition-colors {{ $article->isSavedBy(auth()->id()) ? 'text-blue-500' : '' }}"
                                data-article-id="{{ $article->id }}">
                            <svg class="w-4 h-4" fill="{{ $article->isSavedBy(auth()->id()) ? 'currentColor' : 'none' }}" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"/>
                            </svg>
                            <span>{{ $article->isSavedBy(auth()->id()) ? 'Saved' : 'Save' }}</span>
                        </button>
                    @else
                        <a href="{{ route('auth') }}" class="flex items-center space-x-1 text-gray-500 hover:text-blue-600">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"/>
                            </svg>
                            <span>Save</span>
                        </a>
                    @endauth
                </div>
            </div>
        </div>

        <!-- Article Content -->
        <div class="bg-white rounded-lg shadow-sm p-8 mb-6">
            @if($article->image_url)
                <img src="{{ $article->image_url }}"
                     alt="{{ $article->title }}"
                     class="w-full h-64 object-cover rounded-lg mb-6"
                     onerror="this.style.display='none';">
            @endif

            <div class="prose max-w-none">
                {!! nl2br(e($article->content)) !!}
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Article Save functionality
function toggleArticleSave(articleId) {
    $.post(`/articles/${articleId}/save`)
        .done(function(data) {
            const btn = $(`.article-save-btn[data-article-id="${articleId}"]`);
            const icon = btn.find('svg');
            const text = btn.find('span');

            if (data.saved) {
                btn.addClass('text-blue-500').removeClass('text-gray-500');
                icon.attr('fill', 'currentColor');
                text.text('Saved');
            } else {
                btn.addClass('text-gray-500').removeClass('text-blue-500');
                icon.attr('fill', 'none');
                text.text('Save');
            }

            // Show SweetAlert2 toast
            showToast(data.saved ? 'success' : 'info', data.message);
        })
        .fail(function() {
            showToast('error', 'Error occurred while saving the article');
        });
}
</script>
@endpush

@endsection