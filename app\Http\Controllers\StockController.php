<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;

class StockController extends Controller
{
    public function getStockPrices()
    {
        $symbols = ['BBCA.JK', 'BBRI.JK', 'BMRI.JK', 'TLKM.JK', 'ASII.JK', 'UNVR.JK'];
        $stockData = [];

        foreach ($symbols as $symbol) {
            $cacheKey = "stock_price_{$symbol}";
            
            // Check cache first (cache for 1 minute to avoid too many API calls)
            $data = Cache::remember($cacheKey, 60, function () use ($symbol) {
                return $this->fetchStockPrice($symbol);
            });
            
            $stockCode = str_replace('.JK', '', $symbol);
            $stockData[$stockCode] = $data;
        }

        return response()->json($stockData);
    }

    private function fetchStockPrice($symbol)
    {
        try {
            // Using Yahoo Finance API (free alternative)
            $response = Http::timeout(10)->get("https://query1.finance.yahoo.com/v8/finance/chart/{$symbol}");
            
            if ($response->successful()) {
                $data = $response->json();
                
                if (isset($data['chart']['result'][0])) {
                    $result = $data['chart']['result'][0];
                    $meta = $result['meta'];
                    
                    $currentPrice = $meta['regularMarketPrice'] ?? 0;
                    $previousClose = $meta['previousClose'] ?? $currentPrice;
                    
                    $change = $currentPrice - $previousClose;
                    $changePercent = $previousClose > 0 ? ($change / $previousClose) * 100 : 0;
                    
                    return [
                        'price' => number_format($currentPrice, 0, ',', ','),
                        'change' => ($change >= 0 ? '+' : '') . number_format($change, 0, ',', ','),
                        'changePercent' => ($changePercent >= 0 ? '+' : '') . number_format($changePercent, 2) . '%',
                        'color' => $change >= 0 ? 'green' : 'red',
                        'timestamp' => now()->toISOString()
                    ];
                }
            }
        } catch (\Exception $e) {
            \Log::error("Error fetching stock price for {$symbol}: " . $e->getMessage());
        }

        // Fallback data if API fails
        return [
            'price' => 'N/A',
            'change' => 'N/A',
            'changePercent' => 'N/A',
            'color' => 'gray',
            'timestamp' => now()->toISOString()
        ];
    }

    public function getStockPricesAlternative()
    {
        // Alternative method using Alpha Vantage (requires API key)
        // You can get a free API key from https://www.alphavantage.co/support/#api-key
        
        $apiKey = env('ALPHA_VANTAGE_API_KEY');
        if (!$apiKey) {
            return $this->getMockData();
        }

        $symbols = ['BBCA.JK', 'BBRI.JK', 'BMRI.JK', 'TLKM.JK', 'ASII.JK', 'UNVR.JK'];
        $stockData = [];

        foreach ($symbols as $symbol) {
            $cacheKey = "stock_price_av_{$symbol}";
            
            $data = Cache::remember($cacheKey, 300, function () use ($symbol, $apiKey) {
                try {
                    $response = Http::timeout(10)->get("https://www.alphavantage.co/query", [
                        'function' => 'GLOBAL_QUOTE',
                        'symbol' => $symbol,
                        'apikey' => $apiKey
                    ]);
                    
                    if ($response->successful()) {
                        $data = $response->json();
                        $quote = $data['Global Quote'] ?? null;
                        
                        if ($quote) {
                            $price = floatval($quote['05. price']);
                            $change = floatval($quote['09. change']);
                            $changePercent = floatval(str_replace('%', '', $quote['10. change percent']));
                            
                            return [
                                'price' => number_format($price, 0, ',', ','),
                                'change' => ($change >= 0 ? '+' : '') . number_format($change, 0, ',', ','),
                                'changePercent' => ($changePercent >= 0 ? '+' : '') . number_format($changePercent, 2) . '%',
                                'color' => $change >= 0 ? 'green' : 'red',
                                'timestamp' => now()->toISOString()
                            ];
                        }
                    }
                } catch (\Exception $e) {
                    \Log::error("Error fetching stock price for {$symbol}: " . $e->getMessage());
                }
                
                return null;
            });
            
            if ($data) {
                $stockCode = str_replace('.JK', '', $symbol);
                $stockData[$stockCode] = $data;
            }
        }

        return response()->json($stockData ?: $this->getMockData());
    }

    private function getMockData()
    {
        // Mock data with realistic Indonesian stock prices
        return [
            'BBCA' => [
                'price' => number_format(8750 + rand(-100, 100), 0, ',', ','),
                'change' => '+' . number_format(rand(10, 150), 0, ',', ','),
                'changePercent' => '+' . number_format(rand(50, 200) / 100, 2) . '%',
                'color' => 'green',
                'timestamp' => now()->toISOString()
            ],
            'BBRI' => [
                'price' => number_format(4210 + rand(-50, 50), 0, ',', ','),
                'change' => '+' . number_format(rand(5, 80), 0, ',', ','),
                'changePercent' => '+' . number_format(rand(30, 180) / 100, 2) . '%',
                'color' => 'green',
                'timestamp' => now()->toISOString()
            ],
            'BMRI' => [
                'price' => number_format(5725 + rand(-75, 75), 0, ',', ','),
                'change' => '-' . number_format(rand(10, 60), 0, ',', ','),
                'changePercent' => '-' . number_format(rand(20, 120) / 100, 2) . '%',
                'color' => 'red',
                'timestamp' => now()->toISOString()
            ],
            'TLKM' => [
                'price' => number_format(3520 + rand(-40, 40), 0, ',', ','),
                'change' => '+' . number_format(rand(15, 90), 0, ',', ','),
                'changePercent' => '+' . number_format(rand(40, 250) / 100, 2) . '%',
                'color' => 'green',
                'timestamp' => now()->toISOString()
            ],
            'ASII' => [
                'price' => number_format(6850 + rand(-80, 80), 0, ',', ','),
                'change' => '-' . number_format(rand(20, 100), 0, ',', ','),
                'changePercent' => '-' . number_format(rand(30, 150) / 100, 2) . '%',
                'color' => 'red',
                'timestamp' => now()->toISOString()
            ],
            'UNVR' => [
                'price' => number_format(2630 + rand(-30, 30), 0, ',', ','),
                'change' => '+' . number_format(rand(5, 50), 0, ',', ','),
                'changePercent' => '+' . number_format(rand(20, 100) / 100, 2) . '%',
                'color' => 'green',
                'timestamp' => now()->toISOString()
            ],
        ];
    }
}
