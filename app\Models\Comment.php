<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Comment extends Model
{
    use HasFactory;

    protected $fillable = ['user_id', 'insight_id', 'parent_comment_id', 'content', 'is_valid'];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function insight()
    {
        return $this->belongsTo(Insight::class);
    }

    public function parent()
    {
        return $this->belongsTo(Comment::class, 'parent_comment_id');
    }

    public function replies()
    {
        return $this->hasMany(Comment::class, 'parent_comment_id');
    }
}