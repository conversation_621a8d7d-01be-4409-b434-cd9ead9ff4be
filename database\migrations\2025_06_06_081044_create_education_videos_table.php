<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('education_videos', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users'); // Video Creator
            $table->string('title');
            $table->text('description')->nullable();
            $table->string('video_url');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('education_videos');
    }
};