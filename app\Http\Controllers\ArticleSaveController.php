<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Article;
use App\Models\ArticleSave;
use Illuminate\Support\Facades\Auth;

class ArticleSaveController extends Controller
{
    public function toggle(Article $article)
    {
        $user = Auth::user();

        // Check if article is already saved by this user
        $existingSave = ArticleSave::where('user_id', $user->id)
                                  ->where('article_id', $article->id)
                                  ->first();

        if ($existingSave) {
            // Unsave the article
            $existingSave->delete();
            $saved = false;
            $message = 'Article removed from saves';
        } else {
            // Save the article
            ArticleSave::create([
                'user_id' => $user->id,
                'article_id' => $article->id
            ]);
            $saved = true;
            $message = 'Article saved successfully';
        }

        // Get updated save count
        $saveCount = $article->saves()->count();

        return response()->json([
            'saved' => $saved,
            'save_count' => $saveCount,
            'message' => $message
        ]);
    }
}
