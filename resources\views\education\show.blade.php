@extends('layout')

@section('title', $education->title . ' - InsightFlow')

@section('content')
<div class="p-6">
    <div class="max-w-4xl mx-auto">
        <!-- Breadcrumb -->
        <nav class="mb-6">
            <ol class="flex items-center space-x-2 text-sm text-gray-500">
                <li><a href="{{ route('home') }}" class="hover:text-gray-700">Home</a></li>
                <li><span>/</span></li>
                <li><a href="{{ route('education.index') }}" class="hover:text-gray-700">Education</a></li>
                <li><span>/</span></li>
                <li class="text-gray-900">{{ Str::limit($education->title, 50) }}</li>
            </ol>
        </nav>

        <!-- Education Content Header -->
        <div class="bg-white rounded-lg shadow-sm p-8 mb-6">
            <div class="mb-4">
                <span class="text-sm text-green-600 bg-green-100 px-3 py-1 rounded">{{ $education->category->name }}</span>
            </div>

            <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ $education->title }}</h1>

            <div class="flex items-center justify-between text-sm text-gray-500 mb-6">
                <div class="flex items-center">
                    <div class="w-8 h-8 rounded-full bg-green-100 mr-3 flex items-center justify-center">
                        <span class="text-green-600 font-semibold text-sm">{{ substr($education->user->name, 0, 1) }}</span>
                    </div>
                    <div>
                        <p class="font-medium text-gray-900">{{ $education->user->name }}</p>
                        <p>{{ $education->created_at->format('F j, Y') }} • {{ $education->created_at->diffForHumans() }}</p>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4">
                    @auth
                        <button onclick="toggleEducationSave({{ $education->id }})"
                                class="education-save-btn flex items-center space-x-1 text-gray-500 hover:text-green-600 transition-colors {{ $education->isSavedBy(auth()->id()) ? 'text-green-500' : '' }}"
                                data-education-id="{{ $education->id }}">
                            <svg class="w-4 h-4" fill="{{ $education->isSavedBy(auth()->id()) ? 'currentColor' : 'none' }}" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"/>
                            </svg>
                            <span>{{ $education->isSavedBy(auth()->id()) ? 'Saved' : 'Save' }}</span>
                        </button>
                    @else
                        <a href="{{ route('auth') }}" class="flex items-center space-x-1 text-gray-500 hover:text-green-600">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"/>
                            </svg>
                            <span>Save</span>
                        </a>
                    @endauth
                </div>
            </div>
        </div>

        <!-- Education Content -->
        <div class="bg-white rounded-lg shadow-sm p-8 mb-6">
            @if($education->video_url)
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Educational Video</h3>
                    <div class="relative w-full h-0 pb-[56.25%] rounded-lg overflow-hidden bg-gray-100">
                        @if(str_contains($education->video_url, 'youtube.com') || str_contains($education->video_url, 'youtu.be'))
                            @php
                                $videoId = '';
                                if (str_contains($education->video_url, 'youtube.com/watch?v=')) {
                                    $videoId = substr($education->video_url, strpos($education->video_url, 'v=') + 2);
                                    $videoId = explode('&', $videoId)[0];
                                } elseif (str_contains($education->video_url, 'youtu.be/')) {
                                    $videoId = substr($education->video_url, strrpos($education->video_url, '/') + 1);
                                    $videoId = explode('?', $videoId)[0];
                                }
                            @endphp
                            @if($videoId)
                                <iframe class="absolute top-0 left-0 w-full h-full"
                                        src="https://www.youtube.com/embed/{{ $videoId }}"
                                        frameborder="0"
                                        allowfullscreen></iframe>
                            @else
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <a href="{{ $education->video_url }}" target="_blank" class="text-green-600 hover:text-green-700">
                                        Watch Video →
                                    </a>
                                </div>
                            @endif
                        @elseif(str_contains($education->video_url, 'vimeo.com'))
                            @php
                                $videoId = substr($education->video_url, strrpos($education->video_url, '/') + 1);
                                $videoId = explode('?', $videoId)[0];
                            @endphp
                            <iframe class="absolute top-0 left-0 w-full h-full"
                                    src="https://player.vimeo.com/video/{{ $videoId }}"
                                    frameborder="0"
                                    allowfullscreen></iframe>
                        @else
                            <div class="absolute inset-0 flex items-center justify-center">
                                <a href="{{ $education->video_url }}" target="_blank" class="text-green-600 hover:text-green-700">
                                    Watch Video →
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            @endif

            @if($education->image_url)
                <img src="{{ $education->image_url }}"
                     alt="{{ $education->title }}"
                     class="w-full h-64 object-cover rounded-lg mb-6"
                     onerror="this.style.display='none';">
            @endif

            <div class="prose max-w-none">
                {!! nl2br(e($education->content)) !!}
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Education Save functionality
function toggleEducationSave(educationId) {
    $.post(`/education/${educationId}/save`)
        .done(function(data) {
            const btn = $(`.education-save-btn[data-education-id="${educationId}"]`);
            const icon = btn.find('svg');
            const text = btn.find('span');

            if (data.saved) {
                btn.addClass('text-green-500').removeClass('text-gray-500');
                icon.attr('fill', 'currentColor');
                text.text('Saved');
            } else {
                btn.addClass('text-gray-500').removeClass('text-green-500');
                icon.attr('fill', 'none');
                text.text('Save');
            }

            // Show SweetAlert2 toast
            showToast(data.saved ? 'success' : 'info', data.message);
        })
        .fail(function() {
            showToast('error', 'Error occurred while saving the education content');
        });
}
</script>
@endpush

@endsection
