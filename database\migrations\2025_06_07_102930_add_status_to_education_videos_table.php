<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('education_videos', function (Blueprint $table) {
            $table->enum('status', ['pending', 'published', 'rejected'])->default('pending')->after('video_url');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('education_videos', function (Blueprint $table) {
            $table->dropColumn('status');
        });
    }
};
