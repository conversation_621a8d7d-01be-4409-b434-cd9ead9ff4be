@extends('layout')

@section('title', 'Saved Content - InsightFlow')

@section('content')
<div class="p-8">
    <div class="mb-8">
        <h1 class="text-2xl font-bold text-gray-800">Saved Content</h1>
        <p class="text-gray-600 mt-2">Your collection of saved insights, articles, and educational content</p>
    </div>

    <!-- Tab Navigation -->
    <div class="mb-6">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8">
                <button class="tab-btn border-b-2 border-blue-500 py-2 px-1 text-sm font-medium text-blue-600" data-tab="insights">
                    Insights ({{ $savedInsights->total() }})
                </button>
                <button class="tab-btn border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="articles">
                    Articles ({{ $savedArticles->total() }})
                </button>
                <button class="tab-btn border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="education">
                    Education ({{ $savedEducation->total() }})
                </button>
            </nav>
        </div>
    </div>

    <!-- Insights Tab -->
    <div id="insights-tab" class="tab-content">
        <div class="space-y-8">
            @forelse ($savedInsights as $insight)
                <div class="bg-white rounded-lg shadow-sm p-8">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 rounded-full bg-blue-100 mr-3 flex items-center justify-center">
                            <span class="text-blue-600 font-semibold text-sm">{{ substr($insight->user->name, 0, 1) }}</span>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center">
                                <h4 class="font-semibold text-gray-900 mr-2">{{ $insight->user->name }}</h4>
                                <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">{{ $insight->category->name }}</span>
                            </div>
                            <p class="text-sm text-gray-500">{{ $insight->created_at->diffForHumans() }}</p>
                        </div>
                    </div>

                    <div class="mb-4">
                        <p class="text-gray-800 leading-relaxed">{{ $insight->content }}</p>
                    </div>

                    <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                        <div class="flex items-center space-x-6">
                            <button onclick="toggleInsightLike({{ $insight->id }})"
                                    class="like-btn flex items-center space-x-2 text-gray-500 hover:text-red-500 transition-colors {{ $insight->isLikedBy(auth()->user()) ? 'text-red-500' : '' }}"
                                    data-insight-id="{{ $insight->id }}">
                                <svg class="w-5 h-5" fill="{{ $insight->isLikedBy(auth()->user()) ? 'currentColor' : 'none' }}" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                                </svg>
                                <span class="text-sm likes-count">{{ $insight->likes_count }}</span>
                            </button>
                        </div>

                        <button onclick="toggleInsightSave({{ $insight->id }})"
                                class="save-btn text-green-500 hover:text-gray-500 transition-colors"
                                data-insight-id="{{ $insight->id }}">
                            <svg class="w-5 h-5" fill="currentColor" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"/>
                            </svg>
                        </button>
                    </div>
                </div>
            @empty
                <div class="bg-white rounded-lg shadow-sm p-12 text-center">
                    <div class="w-20 h-20 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                        <svg class="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-medium text-gray-900 mb-2">No saved insights yet</h3>
                    <p class="text-gray-500 mb-4">Start saving insights that interest you to build your personal collection</p>
                    <a href="{{ route('home') }}" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        Browse Insights
                    </a>
                </div>
            @endforelse

            @if($savedInsights->hasPages())
                <div class="mt-6">
                    {{ $savedInsights->links() }}
                </div>
            @endif
        </div>
    </div>

    <!-- Articles Tab -->
    <div id="articles-tab" class="tab-content hidden">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @forelse($savedArticles as $article)
                <div class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                    @if($article->image_url)
                        <img src="{{ $article->image_url }}"
                             alt="{{ $article->title }}"
                             class="w-full h-48 object-cover"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                        <div class="w-full h-48 bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center" style="display: none;">
                            <span class="text-white text-2xl font-bold">{{ substr($article->title, 0, 1) }}</span>
                        </div>
                    @else
                        <div class="w-full h-48 bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                            <span class="text-white text-2xl font-bold">{{ substr($article->title, 0, 1) }}</span>
                        </div>
                    @endif

                    <div class="p-6">
                        <div class="flex items-center mb-3">
                            <span class="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">{{ $article->category->name }}</span>
                            <span class="text-xs text-gray-500 ml-auto">{{ $article->created_at->format('M d, Y') }}</span>
                        </div>

                        <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">{{ $article->title }}</h3>
                        <p class="text-gray-600 text-sm mb-4 line-clamp-3">{{ Str::limit(strip_tags($article->content), 120) }}</p>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-6 h-6 rounded-full bg-blue-100 mr-2 flex items-center justify-center">
                                    <span class="text-blue-600 font-semibold text-xs">{{ substr($article->user->name, 0, 1) }}</span>
                                </div>
                                <span class="text-sm text-gray-500">{{ $article->user->name }}</span>
                            </div>

                            <div class="flex items-center space-x-2">
                                <a href="{{ route('articles.show', $article) }}" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                                    Read More →
                                </a>
                                <button onclick="toggleArticleSave({{ $article->id }})"
                                        class="text-green-500 hover:text-gray-500 transition-colors"
                                        data-article-id="{{ $article->id }}">
                                    <svg class="w-4 h-4" fill="currentColor" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            @empty
                <div class="col-span-full bg-white rounded-lg shadow-sm p-12 text-center">
                    <div class="w-16 h-16 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No saved articles yet</h3>
                    <p class="text-gray-500 mb-4">Start saving articles that interest you</p>
                    <a href="{{ route('articles.index') }}" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        Browse Articles
                    </a>
                </div>
            @endforelse
        </div>

        @if($savedArticles->hasPages())
            <div class="mt-8">
                {{ $savedArticles->links() }}
            </div>
        @endif
    </div>

    <!-- Education Tab -->
    <div id="education-tab" class="tab-content hidden">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @forelse($savedEducation as $education)
                <div class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                    <div class="relative">
                        @if($education->image_url)
                            <img src="{{ $education->image_url }}"
                                 alt="{{ $education->title }}"
                                 class="w-full h-48 object-cover"
                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="w-full h-48 bg-gradient-to-br from-green-400 to-blue-500 flex items-center justify-center" style="display: none;">
                                <span class="text-white text-2xl font-bold">{{ substr($education->title, 0, 1) }}</span>
                            </div>
                        @else
                            <div class="w-full h-48 bg-gradient-to-br from-green-400 to-blue-500 flex items-center justify-center">
                                <span class="text-white text-2xl font-bold">{{ substr($education->title, 0, 1) }}</span>
                            </div>
                        @endif

                        @if($education->video_url)
                            <div class="absolute top-3 right-3 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs flex items-center">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M2 6a2 2 0 012-2h6l2 2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"/>
                                </svg>
                                Video
                            </div>
                        @endif
                    </div>

                    <div class="p-6">
                        <div class="flex items-center mb-3">
                            <span class="text-xs text-green-600 bg-green-100 px-2 py-1 rounded">{{ $education->category->name }}</span>
                            <span class="text-xs text-gray-500 ml-auto">{{ $education->created_at->format('M d, Y') }}</span>
                        </div>

                        <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">{{ $education->title }}</h3>
                        <p class="text-gray-600 text-sm mb-4 line-clamp-3">{{ Str::limit(strip_tags($education->content), 120) }}</p>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-6 h-6 rounded-full bg-green-100 mr-2 flex items-center justify-center">
                                    <span class="text-green-600 font-semibold text-xs">{{ substr($education->user->name, 0, 1) }}</span>
                                </div>
                                <span class="text-sm text-gray-500">{{ $education->user->name }}</span>
                            </div>

                            <div class="flex items-center space-x-2">
                                <a href="{{ route('education.show', $education) }}" class="text-green-600 hover:text-green-700 text-sm font-medium">
                                    Read More →
                                </a>
                                <button onclick="toggleEducationSave({{ $education->id }})"
                                        class="text-green-500 hover:text-gray-500 transition-colors"
                                        data-education-id="{{ $education->id }}">
                                    <svg class="w-4 h-4" fill="currentColor" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            @empty
                <div class="col-span-full bg-white rounded-lg shadow-sm p-12 text-center">
                    <div class="w-16 h-16 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No saved education content yet</h3>
                    <p class="text-gray-500 mb-4">Start saving educational content that interests you</p>
                    <a href="{{ route('education.index') }}" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:bg-green-700 transition-colors">
                        Browse Education
                    </a>
                </div>
            @endforelse
        </div>

        @if($savedEducation->hasPages())
            <div class="mt-8">
                {{ $savedEducation->links() }}
            </div>
        @endif
    </div>
</div>

@push('scripts')
<script>
// Tab functionality
$(document).ready(function() {
    $('.tab-btn').click(function() {
        const tabName = $(this).data('tab');

        // Update tab buttons
        $('.tab-btn').removeClass('border-blue-500 text-blue-600').addClass('border-transparent text-gray-500');
        $(this).removeClass('border-transparent text-gray-500').addClass('border-blue-500 text-blue-600');

        // Update tab content
        $('.tab-content').addClass('hidden');
        $(`#${tabName}-tab`).removeClass('hidden');
    });
});

// Insight Like functionality is now handled by the global toggleInsightLike function

// Insight Save functionality
function toggleInsightSave(insightId) {
    $.post(`/insights/${insightId}/save`)
        .done(function(data) {
            if (!data.saved) {
                // Show toast before removing
                showToast('info', 'Insight removed from saves');

                // Remove the insight from the page when unsaved
                $(`.save-btn[data-insight-id="${insightId}"]`).closest('.bg-white').fadeOut(300, function() {
                    $(this).remove();

                    // Check if no more insights in current tab
                    if ($('#insights-tab .bg-white').length === 0) {
                        location.reload();
                    }
                });
            } else {
                showToast('success', 'Insight saved successfully');
            }
        })
        .fail(function() {
            showToast('error', 'Error occurred while saving the insight');
        });
}

// Article Save functionality
function toggleArticleSave(articleId) {
    $.post(`/articles/${articleId}/save`)
        .done(function(data) {
            if (!data.saved) {
                // Show toast before removing
                showToast('info', 'Article removed from saves');

                // Remove the article from the page when unsaved
                $(`button[data-article-id="${articleId}"]`).closest('.bg-white').fadeOut(300, function() {
                    $(this).remove();

                    // Check if no more articles in current tab
                    if ($('#articles-tab .bg-white').length === 0) {
                        location.reload();
                    }
                });
            } else {
                showToast('success', 'Article saved successfully');
            }
        })
        .fail(function() {
            showToast('error', 'Error occurred while saving the article');
        });
}

// Education Save functionality
function toggleEducationSave(educationId) {
    $.post(`/education/${educationId}/save`)
        .done(function(data) {
            if (!data.saved) {
                // Show toast before removing
                showToast('info', 'Education content removed from saves');

                // Remove the education content from the page when unsaved
                $(`button[data-education-id="${educationId}"]`).closest('.bg-white').fadeOut(300, function() {
                    $(this).remove();

                    // Check if no more education content in current tab
                    if ($('#education-tab .bg-white').length === 0) {
                        location.reload();
                    }
                });
            } else {
                showToast('success', 'Education content saved successfully');
            }
        })
        .fail(function() {
            showToast('error', 'Error occurred while saving the education content');
        });
}
</script>
@endpush
@endsection
