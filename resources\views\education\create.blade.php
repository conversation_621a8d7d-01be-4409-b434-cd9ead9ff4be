@extends('layout')

@section('title', 'Create Education Content - InsightFlow')

@section('content')
<div class="p-8">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Create Education Content</h1>
                    <p class="text-gray-600 mt-2">Share your knowledge with the community</p>
                </div>
                <a href="{{ route('education.index') }}" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                    Back to Education
                </a>
            </div>
        </div>

        <!-- Form -->
        <div class="bg-white rounded-lg shadow-sm p-8">
            <form action="{{ route('education.store') }}" method="POST" class="space-y-6">
                @csrf
                
                <!-- Title -->
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Title *</label>
                    <input type="text" 
                           id="title" 
                           name="title" 
                           value="{{ old('title') }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('title') border-red-500 @enderror"
                           placeholder="Enter a compelling title for your educational content"
                           required>
                    @error('title')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Category -->
                <div>
                    <label for="category_id" class="block text-sm font-medium text-gray-700 mb-2">Category *</label>
                    <input type="hidden" name="category_id" id="category_id" value="{{ old('category_id') }}" required>
                    <x-dropdown
                        trigger="Select a category"
                        :icon="true"
                        button-class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white hover:bg-gray-50 text-gray-700 justify-between @error('category_id') border-red-500 @enderror"
                        width="w-full"
                    >
                        <x-dropdown-item onclick="selectEducationCategory('', 'Select a category')">
                            Select a category
                        </x-dropdown-item>
                        @foreach($categories as $category)
                            <x-dropdown-item onclick="selectEducationCategory('{{ $category->id }}', '{{ $category->name }}')">
                                {{ $category->name }}
                            </x-dropdown-item>
                        @endforeach
                    </x-dropdown>
                    @error('category_id')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Image URL -->
                <div>
                    <label for="image_url" class="block text-sm font-medium text-gray-700 mb-2">Featured Image URL</label>
                    <input type="url"
                           id="image_url"
                           name="image_url"
                           value="{{ old('image_url') }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('image_url') border-red-500 @enderror"
                           placeholder="https://example.com/image.jpg">
                    @error('image_url')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                    <p class="text-gray-500 text-sm mt-1">Optional: Add a featured image to make your content more engaging</p>
                </div>

                <!-- Video URL -->
                <div>
                    <label for="video_url" class="block text-sm font-medium text-gray-700 mb-2">Educational Video URL</label>
                    <input type="url"
                           id="video_url"
                           name="video_url"
                           value="{{ old('video_url') }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('video_url') border-red-500 @enderror"
                           placeholder="https://youtube.com/watch?v=... or https://vimeo.com/...">
                    @error('video_url')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                    <p class="text-gray-500 text-sm mt-1">Optional: Add an educational video (YouTube, Vimeo, etc.) to enhance learning</p>
                </div>

                <!-- Content -->
                <div>
                    <label for="content" class="block text-sm font-medium text-gray-700 mb-2">Content *</label>
                    <textarea id="content" 
                              name="content" 
                              rows="15"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('content') border-red-500 @enderror"
                              placeholder="Write your educational content here. You can include explanations, examples, tips, and any valuable information that will help others learn."
                              required>{{ old('content') }}</textarea>
                    @error('content')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                    <p class="text-gray-500 text-sm mt-1">Provide detailed, helpful content that educates readers about financial topics</p>
                </div>

                <!-- Submission Notice -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                        </svg>
                        <div>
                            <h4 class="text-blue-800 font-medium">Review Process</h4>
                            <p class="text-blue-700 text-sm mt-1">
                                Your educational content will be submitted for admin review. It will be published on the Education page after approval to ensure quality and accuracy.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex items-center justify-between pt-6 border-t">
                    <a href="{{ route('education.index') }}" class="text-gray-600 hover:text-gray-800">
                        Cancel
                    </a>
                    <button type="submit" class="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                        Submit for Review
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function selectEducationCategory(categoryId, categoryName) {
    document.getElementById('category_id').value = categoryId;
    // Update the dropdown trigger text
    const trigger = document.querySelector('[x-ref="button"] span');
    if (trigger) {
        trigger.textContent = categoryName;
    }
}
</script>
@endpush
