@extends('layout')

@section('title', 'Home - InsightFlow')

@section('content')
    <!-- Hero Section -->
    <div class="bg-gradient-to-br from-blue-50 via-white to-indigo-50 border-b border-gray-100">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 lg:py-12">
            <div class="text-center mb-8">
                <h1 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-3">
                    Welcome to <span class="text-blue-600">InsightFlow</span>
                </h1>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                    Share market insights, discover investment opportunities, and learn from the community
                </p>
            </div>

            <!-- Stock Market Overview -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-8">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                        <svg class="w-6 h-6 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                        </svg>
                        Indonesian Stock Market
                    </h2>
                    <div class="text-sm text-gray-500 bg-gray-50 px-3 py-1 rounded-full">
                        Live Prices
                    </div>
                </div>
                <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4">
                @php
                    $stockInfo = [
                        'BBCA' => [
                            'name' => 'Bank Central Asia',
                            'logo' => 'https://logo.clearbit.com/bca.co.id',
                        ],
                        'BBRI' => [
                            'name' => 'Bank Rakyat Indonesia',
                            'logo' => 'https://logo.clearbit.com/bri.co.id',
                        ],
                        'BMRI' => [
                            'name' => 'Bank Mandiri',
                            'logo' => 'https://logo.clearbit.com/bankmandiri.co.id',
                        ],
                        'TLKM' => [
                            'name' => 'Telkom Indonesia',
                            'logo' => 'https://logo.clearbit.com/telkom.co.id',
                        ],
                        'ASII' => [
                            'name' => 'Astra International',
                            'logo' => 'https://logo.clearbit.com/astra.co.id',
                        ],
                        'UNVR' => [
                            'name' => 'Unilever Indonesia',
                            'logo' => 'https://logo.clearbit.com/unilever.co.id',
                        ],
                    ];
                @endphp

                @foreach ($stockSymbols as $symbol)
                    @php
                        $stockCode = str_replace('IDX:', '', $symbol);
                        $info = $stockInfo[$stockCode] ?? [
                            'name' => 'Unknown Company',
                            'logo' => null,
                        ];
                    @endphp
                    <div class="bg-gradient-to-br from-white to-gray-50 rounded-xl border border-gray-200 p-4 hover:shadow-lg hover:border-blue-200 transition-all duration-300 group"
                        data-symbol="{{ $symbol }}">
                        <div class="text-center">
                            <!-- Company Logo -->
                            <div class="flex justify-center mb-3">
                                @if ($info['logo'])
                                    <div class="relative">
                                        <img src="{{ $info['logo'] }}" alt="{{ $stockCode }} logo"
                                            class="w-10 h-10 rounded-full object-cover ring-2 ring-gray-100 group-hover:ring-blue-200 transition-all"
                                            onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                        <div class="w-10 h-10 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center text-blue-700 font-bold text-sm ring-2 ring-gray-100 group-hover:ring-blue-200 transition-all"
                                            style="display: none;">
                                            {{ substr($stockCode, 0, 2) }}
                                        </div>
                                    </div>
                                @else
                                    <div class="w-10 h-10 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center text-blue-700 font-bold text-sm ring-2 ring-gray-100 group-hover:ring-blue-200 transition-all">
                                        {{ substr($stockCode, 0, 2) }}
                                    </div>
                                @endif
                            </div>

                            <!-- Stock Code -->
                            <div class="text-sm font-bold text-gray-900 mb-2 tracking-wide">{{ $stockCode }}</div>
                            <div class="text-xs text-gray-500 mb-3 line-clamp-1">{{ $info['name'] }}</div>

                            <!-- Price (will be updated by JavaScript) -->
                            <div class="stock-price text-lg font-bold text-gray-900 mb-1">
                                <div class="animate-pulse bg-gradient-to-r from-gray-200 to-gray-300 h-6 w-16 mx-auto rounded-lg"></div>
                            </div>

                            <!-- Change (will be updated by JavaScript) -->
                            <div class="stock-change text-sm">
                                <div class="animate-pulse bg-gradient-to-r from-gray-200 to-gray-300 h-4 w-12 mx-auto rounded-lg"></div>
                            </div>
                        </div>
                    </div>
                @endforeach
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Mobile Analytics Cards Section (visible only on mobile) -->
        <div class="lg:hidden grid grid-cols-1 gap-6 mb-8">
            <!-- IHSG Chart -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow">
                <div class="flex items-center mb-4">
                    <div>
                        <h3 class="font-semibold text-gray-900">Index Harga Saham Gabungan</h3>
                        <p class="text-sm text-gray-500">IDX Composite Index</p>
                    </div>
                </div>
                <div id="ihsg-chart-mobile" class="h-48 rounded-lg overflow-hidden"></div>
            </div>

            <!-- Most Liked Insights -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow">
                <div class="flex items-center mb-4">
                    <div>
                        <h3 class="font-semibold text-gray-900">Trending Insights</h3>
                        <p class="text-sm text-gray-500">Most liked by community</p>
                    </div>
                </div>
                <div class="most-liked-container">
                    @include('partials.most-liked-insights', ['mostLikedInsights' => $mostLikedInsights])
                </div>
            </div>

            <!-- Category Analytics -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow">
                <div class="flex items-center mb-4">
                    <div class="w-10 h-10 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"/>
                        </svg>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-900">Popular Topics</h3>
                        <p class="text-sm text-gray-500">Most discussed categories</p>
                    </div>
                </div>
                <div class="space-y-3">
                    @forelse($categoryStats as $category)
                        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-3 hover:shadow-sm transition-shadow">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-medium text-gray-900 text-sm">{{ Str::limit($category->name, 20) }}</h4>
                                <span class="text-xs font-semibold text-blue-600 bg-blue-100 px-2 py-1 rounded-full">{{ $category->insights_count }}</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-500" style="width: {{ min(100, ($category->insights_count / max($categoryStats->max('insights_count'), 1)) * 100) }}%"></div>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-6">
                            <div class="w-12 h-12 bg-gray-100 rounded-full mx-auto mb-3 flex items-center justify-center">
                                <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a1.994 1.994 0 01-1.414.586H7a1 1 0 01-1-1V3a1 1 0 011-1z"/>
                                </svg>
                            </div>
                            <p class="text-gray-500 text-sm">No categories yet</p>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- Desktop Layout: Main Content + Sidebar -->
        <div class="flex flex-col lg:flex-row lg:items-start gap-8">
            <!-- Main Insights Section with Constrained Width -->
            <div class="flex-1 lg:max-w-4xl">
                <!-- Insights Header -->
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center">
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900">Community Insights</h2>
                            <p class="text-sm text-gray-500">Share and discover market perspectives</p>
                        </div>
                    </div>
                    <div class="hidden sm:flex items-center space-x-3">
                        <div class="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                            {{ $insights->total() }} insights
                        </div>
                    </div>
                </div>

                <!-- Post Form -->
                @auth
                    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-8" id="insight-form">
                        <div class="flex items-center mb-4">
                            <div class="w-8 h-8 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center mr-3">
                                <span class="text-blue-600 font-semibold text-sm">{{ substr(auth()->user()->name, 0, 1) }}</span>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900">Share Your Insight</h3>
                                <p class="text-sm text-gray-500">What's your take on the market?</p>
                            </div>
                        </div>

                        <form action="{{ route('insights.store') }}" method="POST">
                            @csrf
                            <div class="space-y-4">
                                <div>
                                    <textarea name="content"
                                        class="w-full p-4 border border-gray-200 rounded-xl resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                        rows="4" placeholder="Share your market insights... (e.g., Analysis on $BBCA, market trends, investment strategies)">{{ old('content') }}</textarea>
                                    @error('content')
                                        <p class="text-red-500 text-sm mt-2">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                                    <div class="flex items-center space-x-3">
                                        <input type="hidden" name="category_id" id="selected-category" value="{{ old('category_id') }}">
                                        <div class="flex items-center space-x-2">
                                            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a1.994 1.994 0 01-1.414.586H7a1 1 0 01-1-1V3a1 1 0 011-1z"/>
                                            </svg>
                                            <x-dropdown
                                                trigger="Select Category"
                                                :icon="true"
                                                button-class="border border-gray-200 rounded-lg px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white hover:bg-gray-50 text-gray-700"
                                            >
                                                <x-dropdown-item onclick="selectCategory('', 'Select Category')">
                                                    Select Category
                                                </x-dropdown-item>
                                                @foreach ($insight_categories as $category)
                                                    <x-dropdown-item onclick="selectCategory('{{ $category->id }}', '{{ $category->name }}')">
                                                        {{ $category->name }}
                                                    </x-dropdown-item>
                                                @endforeach
                                            </x-dropdown>
                                        </div>
                                    </div>

                                    <button type="submit"
                                        class="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-2 rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200 font-medium shadow-sm hover:shadow-md">
                                        Share Insight
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                @else
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6 mb-8 text-center">
                        <div class="w-12 h-12 bg-blue-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Join the Conversation</h3>
                        <p class="text-gray-600 mb-4">Sign in to share your market insights and connect with the community</p>
                        <a href="{{ route('auth') }}" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                            Sign In to Share
                        </a>
                    </div>
                @endauth

                <!-- Search Interface -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4 mb-6">
                    <div class="flex flex-col sm:flex-row sm:items-center gap-4">
                        <div class="flex-1">
                            <div class="relative">
                                <input type="text"
                                       id="search-insights"
                                       placeholder="Search insights by content or author..."
                                       value="{{ request('search') }}"
                                       class="w-full border border-gray-300 rounded-lg px-4 py-3 pr-12 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <div class="absolute inset-y-0 right-0 flex items-center pr-4">
                                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center gap-3">
                            <div class="text-sm text-gray-500">
                                <span id="insights-count">{{ $insights->total() }}</span> insights found
                            </div>
                            <button id="clear-search" class="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                                Clear
                            </button>
                        </div>
                    </div>
                    <div id="search-loading" class="hidden mt-3">
                        <div class="flex items-center text-sm text-gray-500">
                            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                            Searching insights...
                        </div>
                    </div>
                </div>

                <!-- Insights Feed -->
                <div id="insights-feed-container" class="space-y-6">
                    @include('partials.insights-feed', ['insights' => $insights])
                </div>

                <!-- Pagination -->
                <div id="insights-pagination" class="mt-6 lg:mt-8">
                    @if ($insights->hasPages())
                        {{ $insights->links() }}
                    @endif
                </div>
            </div>

            <!-- Desktop Analytics Sidebar (visible only on desktop) -->
            <div class="hidden lg:flex lg:flex-col lg:w-80 lg:flex-shrink-0">
                <div class="space-y-6 sticky top-8">
                    <!-- IHSG Chart -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow">
                        <div class="flex items-center mb-4">
                            <div>
                                <h3 class="font-semibold text-gray-900">Index Harga Saham Gabungan</h3>
                                <p class="text-sm text-gray-500">IDX Composite</p>
                            </div>
                        </div>
                        <div id="ihsg-chart" class="h-48 rounded-lg overflow-hidden"></div>
                    </div>

                    <!-- Most Liked Insights -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow">
                        <div class="flex items-center mb-4">
                            <div>
                                <h3 class="font-semibold text-gray-900">Trending Insights</h3>
                                <p class="text-sm text-gray-500">Most liked by community</p>
                            </div>
                        </div>
                        <div class="most-liked-container">
                            @include('partials.most-liked-insights', ['mostLikedInsights' => $mostLikedInsights])
                        </div>
                    </div>

                    <!-- Category Analytics -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow">
                        <div class="flex items-center mb-4">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg flex items-center justify-center mr-3">
                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"/>
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900">Popular Topics</h3>
                                <p class="text-sm text-gray-500">Most discussed categories</p>
                            </div>
                        </div>
                        <div class="space-y-3">
                            @forelse($categoryStats as $category)
                                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-3 hover:shadow-sm transition-shadow">
                                    <div class="flex items-center justify-between mb-2">
                                        <h4 class="font-medium text-gray-900 text-sm">{{ Str::limit($category->name, 20) }}</h4>
                                        <span class="text-xs font-semibold text-blue-600 bg-blue-100 px-2 py-1 rounded-full">{{ $category->insights_count }}</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-500" style="width: {{ min(100, ($category->insights_count / max($categoryStats->max('insights_count'), 1)) * 100) }}%"></div>
                                    </div>
                                </div>
                            @empty
                                <div class="text-center py-6">
                                    <div class="w-12 h-12 bg-gray-100 rounded-full mx-auto mb-3 flex items-center justify-center">
                                        <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a1.994 1.994 0 01-1.414.586H7a1 1 0 01-1-1V3a1 1 0 011-1z"/>
                                        </svg>
                                    </div>
                                    <p class="text-gray-500 text-sm">No categories yet</p>
                                </div>
                            @endforelse
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

    @push('scripts')
        <script>
            // Category selection for insight form
            function selectCategory(categoryId, categoryName) {
                document.getElementById('selected-category').value = categoryId;
                // Update the dropdown trigger text
                const trigger = document.querySelector('[x-ref="button"] span');
                if (trigger) {
                    trigger.textContent = categoryName;
                }
            }

            // Like functionality - using standardized function
            function toggleLike(insightId) {
                toggleInsightLike(insightId);
            }

            // Save functionality
            function toggleSave(insightId) {
                $.post(`/insights/${insightId}/save`)
                    .done(function(data) {
                        const btn = $(`.save-btn[data-insight-id="${insightId}"]`);
                        const icon = btn.find('svg');
                        const count = btn.find('.saves-count');

                        if (data.saved) {
                            btn.removeClass('text-gray-500 hover:text-green-500 hover:bg-green-50')
                               .addClass('text-green-600 bg-green-50 hover:bg-green-100');
                            icon.attr('fill', 'currentColor');
                        } else {
                            btn.removeClass('text-green-600 bg-green-50 hover:bg-green-100')
                               .addClass('text-gray-500 hover:text-green-500 hover:bg-green-50');
                            icon.attr('fill', 'none');
                        }

                        count.text(data.saves_count);

                        // Show SweetAlert2 toast
                        showToast(data.saved ? 'success' : 'info', data.message);
                    })
                    .fail(function() {
                        showToast('error', 'Error occurred while saving the insight');
                    });
            }

            // Real-time stock prices
            function fetchStockPrices() {
                $.ajax({
                    url: '{{ route('api.stock-prices') }}',
                    method: 'GET',
                    timeout: 10000,
                    success: function(data) {
                        updateStockDisplay(data);
                    },
                    error: function(xhr, status, error) {
                        console.error('Error fetching stock prices:', error);
                        // Show error state
                        $('.stock-price').each(function() {
                            $(this).html('<span class="text-red-500 text-sm">Error</span>');
                        });
                        $('.stock-change').each(function() {
                            $(this).html('<span class="text-red-500 text-xs">Failed to load</span>');
                        });
                    }
                });
            }

            function updateStockDisplay(stockData) {
                // Update each stock card with real data
                $('[data-symbol]').each(function() {
                    const symbol = $(this).data('symbol');
                    const stockCode = symbol.replace('IDX:', '');
                    const data = stockData[stockCode];

                    if (data) {
                        // Update price
                        $(this).find('.stock-price').html(data.price);

                        // Update change with color
                        const changeClass = data.color === 'green' ? 'text-green-600' :
                            data.color === 'red' ? 'text-red-600' : 'text-gray-500';

                        $(this).find('.stock-change')
                            .removeClass('text-gray-500 text-green-600 text-red-600')
                            .addClass(changeClass)
                            .html(data.changePercent);
                    } else {
                        // Fallback if no data for this stock
                        $(this).find('.stock-price').html('<span class="text-gray-500">N/A</span>');
                        $(this).find('.stock-change').html('<span class="text-gray-500 text-xs">No data</span>');
                    }
                });

                // Update timestamp
                const now = new Date().toLocaleTimeString();
                console.log('Stock prices updated at:', now);
            }

            // Auto-refresh stock prices
            function startStockPriceUpdates() {
                // Initial load
                fetchStockPrices();

                // Update every 30 seconds
                setInterval(fetchStockPrices, 30000);
            }

            // Start when page loads
            $(document).ready(function() {
                startStockPriceUpdates();
            });

            // Search functionality for insights
            let searchTimeout;

            function performInsightSearch() {
                const search = document.getElementById('search-insights').value;

                // Show loading indicator
                document.getElementById('search-loading').classList.remove('hidden');

                // Build query parameters
                const params = new URLSearchParams();
                if (search) params.append('search', search);

                // Make AJAX request
                fetch(`{{ route('home') }}?${params.toString()}`, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    // Update insights feed
                    document.getElementById('insights-feed-container').innerHTML = data.html;

                    // Update pagination
                    document.getElementById('insights-pagination').innerHTML = data.pagination;

                    // Update count
                    document.getElementById('insights-count').textContent = data.total;

                    // Update URL without page reload
                    const newUrl = `{{ route('home') }}${params.toString() ? '?' + params.toString() : ''}`;
                    window.history.pushState({}, '', newUrl);
                })
                .catch(error => {
                    console.error('Search error:', error);
                    showToast('error', 'Search failed. Please try again.');
                })
                .finally(() => {
                    // Hide loading indicator
                    document.getElementById('search-loading').classList.add('hidden');
                });
            }

            // Search input with debouncing
            document.getElementById('search-insights').addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(performInsightSearch, 300);
            });

            // Clear search
            document.getElementById('clear-search').addEventListener('click', function() {
                document.getElementById('search-insights').value = '';
                performInsightSearch();
            });

            // Clear search function for empty state
            function clearSearch() {
                document.getElementById('search-insights').value = '';
                performInsightSearch();
            }

            // Handle pagination clicks
            document.addEventListener('click', function(e) {
                if (e.target.closest('#insights-pagination a')) {
                    e.preventDefault();
                    const url = e.target.closest('a').href;
                    const urlParams = new URL(url).searchParams;

                    // Add current search parameters
                    const search = document.getElementById('search-insights').value;
                    if (search) urlParams.set('search', search);

                    // Show loading
                    document.getElementById('search-loading').classList.remove('hidden');

                    fetch(`{{ route('home') }}?${urlParams.toString()}`, {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        document.getElementById('insights-feed-container').innerHTML = data.html;
                        document.getElementById('insights-pagination').innerHTML = data.pagination;
                        document.getElementById('insights-count').textContent = data.total;

                        // Update URL
                        window.history.pushState({}, '', `{{ route('home') }}?${urlParams.toString()}`);

                        // Scroll to top of insights
                        document.getElementById('insights-feed-container').scrollIntoView({ behavior: 'smooth' });
                    })
                    .catch(error => console.error('Pagination error:', error))
                    .finally(() => {
                        document.getElementById('search-loading').classList.add('hidden');
                    });
                }
            });

            // IHSG Charts - Desktop and Mobile
            // Desktop Chart
            new TradingView.widget({
                "width": "100%",
                "height": 192,
                "symbol": "IDX:COMPOSITE",
                "interval": "D",
                "timezone": "Asia/Jakarta",
                "theme": "light",
                "style": "1",
                "locale": "en",
                "toolbar_bg": "#f1f3f6",
                "enable_publishing": false,
                "allow_symbol_change": false,
                "container_id": "ihsg-chart",
                "hide_top_toolbar": true,
                "hide_legend": true,
                "save_image": false,
                "fontFamily": "Trebuchet MS, sans-serif",
                "fontSize": "12"
            });

            // Mobile Chart
            new TradingView.widget({
                "width": "100%",
                "height": 192,
                "symbol": "IDX:COMPOSITE",
                "interval": "D",
                "timezone": "Asia/Jakarta",
                "theme": "light",
                "style": "1",
                "locale": "en",
                "toolbar_bg": "#f1f3f6",
                "enable_publishing": false,
                "allow_symbol_change": false,
                "container_id": "ihsg-chart-mobile",
                "hide_top_toolbar": true,
                "hide_legend": true,
                "save_image": false,
                "fontFamily": "Trebuchet MS, sans-serif",
                "fontSize": "12"
            });
            
        </script>
    @endpush
@endsection
