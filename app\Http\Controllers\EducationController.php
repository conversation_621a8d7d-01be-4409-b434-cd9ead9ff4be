<?php

namespace App\Http\Controllers;

use App\Models\Education;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class EducationController extends Controller
{
    public function index(Request $request)
    {
        // Start with published education content query
        $query = Education::with(['user', 'category'])
                          ->where('status', 'published');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('category', function($catQuery) use ($search) {
                      $catQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Apply category filter if provided
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        // Get filtered education content
        $educations = $query->latest()->paginate(12)->appends($request->query());

        // For AJAX requests, return JSON
        if ($request->ajax()) {
            return response()->json([
                'html' => view('partials.education-grid', compact('educations'))->render(),
                'pagination' => $educations->links()->toHtml(),
                'total' => $educations->total()
            ]);
        }

        // Get education categories for filtering
        $categories = Category::where('type', 'education')->get();

        // Get selected category for UI
        $selectedCategory = null;
        if ($request->filled('category')) {
            $selectedCategory = $categories->firstWhere('id', $request->category);
        }

        return view('education.index', [
            'educations' => $educations,
            'categories' => $categories,
            'selectedCategory' => $selectedCategory
        ]);
    }

    public function show(Education $education)
    {
        // Check if user can view this education content
        if ($education->status !== 'published' &&
            (!Auth::check() || (Auth::user()->id !== $education->user_id && Auth::user()->role->name !== 'Admin'))) {
            abort(404);
        }

        return view('education.show', compact('education'));
    }

    public function create()
    {
        $categories = Category::where('type', 'education')->get();
        return view('education.create', compact('categories'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'category_id' => 'required|exists:categories,id',
            'image_url' => 'nullable|url',
            'video_url' => 'nullable|url'
        ]);

        // Verify the category is education type
        $category = Category::findOrFail($request->category_id);
        if ($category->type !== 'education') {
            abort(422, 'Invalid category for education content.');
        }

        Education::create([
            'user_id' => Auth::id(),
            'title' => $request->title,
            'content' => $request->content,
            'category_id' => $request->category_id,
            'image_url' => $request->image_url,
            'video_url' => $request->video_url,
            'status' => 'pending' // Always starts as pending for admin approval
        ]);

        return redirect()->route('education.my-content')
                        ->with('success', 'Education content submitted for review. It will be published after admin approval.');
    }

    public function myContent()
    {
        $educations = Education::with(['category'])
                              ->where('user_id', Auth::id())
                              ->latest()
                              ->paginate(10);

        return view('education.my-content', compact('educations'));
    }

    public function edit(Education $education)
    {
        // Only author can edit their own education content, and only if pending
        if ($education->user_id !== Auth::id() || $education->status !== 'pending') {
            abort(403, 'You can only edit your own pending education content.');
        }

        $categories = Category::where('type', 'education')->get();
        return view('education.edit', compact('education', 'categories'));
    }

    public function update(Request $request, Education $education)
    {
        // Only author can update their own education content, and only if pending
        if ($education->user_id !== Auth::id() || $education->status !== 'pending') {
            abort(403, 'You can only edit your own pending education content.');
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'category_id' => 'required|exists:categories,id',
            'image_url' => 'nullable|url',
            'video_url' => 'nullable|url'
        ]);

        // Verify the category is education type
        $category = Category::findOrFail($request->category_id);
        if ($category->type !== 'education') {
            abort(422, 'Invalid category for education content.');
        }

        $education->update([
            'title' => $request->title,
            'content' => $request->content,
            'category_id' => $request->category_id,
            'image_url' => $request->image_url,
            'video_url' => $request->video_url,
        ]);

        return redirect()->route('education.my-content')
                        ->with('success', 'Education content updated successfully.');
    }

    public function destroy(Education $education)
    {
        // Only author can delete their own education content, and only if pending
        if ($education->user_id !== Auth::id() || $education->status !== 'pending') {
            abort(403, 'You can only delete your own pending education content.');
        }

        $education->delete();

        return redirect()->route('education.my-content')
                        ->with('success', 'Education content deleted successfully.');
    }
}
