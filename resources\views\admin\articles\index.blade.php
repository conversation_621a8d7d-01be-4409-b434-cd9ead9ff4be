@extends('admin.layout')

@section('title', 'Article Management - Admin Panel')
@section('page-title', 'Article Management')
@section('page-description', 'Review and manage user-submitted articles')

@section('content')
<div class="space-y-6">
    <!-- Filter Tabs -->
    <div class="bg-white rounded-lg shadow-sm">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6">
                <button class="filter-tab border-b-2 border-blue-500 py-4 px-1 text-sm font-medium text-blue-600" data-status="all">
                    All Articles
                </button>
                <button class="filter-tab border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300" data-status="pending">
                    Pending Review
                </button>
                <button class="filter-tab border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300" data-status="published">
                    Published
                </button>
                <button class="filter-tab border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300" data-status="rejected">
                    Rejected
                </button>
            </nav>
        </div>
    </div>

    <!-- Articles Table -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">Articles</h3>
                    <div class="text-sm text-gray-500 ml-4">
                        <span id="article-count">{{ $articles->total() }}</span> articles found
                    </div>
                </div>
                <div class="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
                    <div class="relative">
                        <input type="text"
                               id="search-articles"
                               placeholder="Search articles..."
                               value="{{ request('search') }}"
                               class="border border-gray-300 rounded-lg px-3 py-2 pr-10 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 w-full sm:w-64">
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                        </div>
                    </div>
                    <button id="clear-filters" class="px-3 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                        Clear
                    </button>
                </div>
            </div>
            <div id="loading-indicator" class="hidden mt-2">
                <div class="flex items-center text-sm text-gray-500">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                    Searching...
                </div>
            </div>
        </div>

        <div id="articles-table-container">
            @include('admin.articles.table', ['articles' => $articles])
        </div>

        <div id="articles-pagination" class="px-6 py-4 border-t border-gray-200">
            @if($articles->hasPages())
                {{ $articles->links() }}
            @endif
        </div>
    </div>
</div>

<!-- Article Details Modal -->
<div id="articleModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center pb-3">
            <h3 class="text-lg font-bold text-gray-900">Article Details</h3>
            <button onclick="closeArticleModal()" class="text-gray-400 hover:text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
        </div>
        <div id="articleModalContent">
            <!-- Article details will be loaded here -->
        </div>
    </div>
</div>

@if(session('success'))
    <div class="fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg shadow-lg z-50">
        {{ session('success') }}
    </div>
@endif

@if(session('error'))
    <div class="fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg shadow-lg z-50">
        {{ session('error') }}
    </div>
@endif
@endsection

@push('scripts')
<script>
    let searchTimeout;
    let currentStatusFilter = 'all';

    // Search and filter functionality
    function performSearch() {
        const search = document.getElementById('search-articles').value;

        // Show loading indicator
        document.getElementById('loading-indicator').classList.remove('hidden');

        // Build query parameters
        const params = new URLSearchParams();
        if (search) params.append('search', search);
        if (currentStatusFilter !== 'all') params.append('status_filter', currentStatusFilter);

        // Make AJAX request
        fetch(`{{ route('admin.articles') }}?${params.toString()}`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            // Update table content
            document.getElementById('articles-table-container').innerHTML = data.html;

            // Update pagination
            document.getElementById('articles-pagination').innerHTML = data.pagination;

            // Update count
            document.getElementById('article-count').textContent = data.total;

            // Update URL without page reload
            const newUrl = `{{ route('admin.articles') }}${params.toString() ? '?' + params.toString() : ''}`;
            window.history.pushState({}, '', newUrl);
        })
        .catch(error => {
            console.error('Search error:', error);
        })
        .finally(() => {
            // Hide loading indicator
            document.getElementById('loading-indicator').classList.add('hidden');
        });
    }

    // Search input with debouncing
    document.getElementById('search-articles').addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(performSearch, 300);
    });

    // Clear filters
    document.getElementById('clear-filters').addEventListener('click', function() {
        document.getElementById('search-articles').value = '';
        currentStatusFilter = 'all';

        // Reset active tab
        document.querySelectorAll('.filter-tab').forEach(t => {
            t.classList.remove('border-blue-500', 'text-blue-600');
            t.classList.add('border-transparent', 'text-gray-500');
        });
        document.querySelector('.filter-tab[data-status="all"]').classList.remove('border-transparent', 'text-gray-500');
        document.querySelector('.filter-tab[data-status="all"]').classList.add('border-blue-500', 'text-blue-600');

        performSearch();
    });

    // Enhanced filter functionality with search integration
    document.querySelectorAll('.filter-tab').forEach(tab => {
        tab.addEventListener('click', function() {
            // Update active tab
            document.querySelectorAll('.filter-tab').forEach(t => {
                t.classList.remove('border-blue-500', 'text-blue-600');
                t.classList.add('border-transparent', 'text-gray-500');
            });
            this.classList.remove('border-transparent', 'text-gray-500');
            this.classList.add('border-blue-500', 'text-blue-600');

            // Update current filter and perform search
            currentStatusFilter = this.dataset.status;
            performSearch();
        });
    });

    // Handle pagination clicks
    document.addEventListener('click', function(e) {
        if (e.target.closest('#articles-pagination a')) {
            e.preventDefault();
            const url = e.target.closest('a').href;
            const urlParams = new URL(url).searchParams;

            // Add current search and filter parameters
            const search = document.getElementById('search-articles').value;

            if (search) urlParams.set('search', search);
            if (currentStatusFilter !== 'all') urlParams.set('status_filter', currentStatusFilter);

            // Show loading
            document.getElementById('loading-indicator').classList.remove('hidden');

            fetch(`{{ route('admin.articles') }}?${urlParams.toString()}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('articles-table-container').innerHTML = data.html;
                document.getElementById('articles-pagination').innerHTML = data.pagination;
                document.getElementById('article-count').textContent = data.total;

                // Update URL
                window.history.pushState({}, '', `{{ route('admin.articles') }}?${urlParams.toString()}`);
            })
            .catch(error => console.error('Pagination error:', error))
            .finally(() => {
                document.getElementById('loading-indicator').classList.add('hidden');
            });
        }
    });

    function viewArticle(articleId) {
        // Show loading state
        document.getElementById('articleModalContent').innerHTML = `
            <div class="flex items-center justify-center py-12">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span class="ml-3 text-gray-600">Loading article details...</span>
            </div>
        `;
        document.getElementById('articleModal').classList.remove('hidden');

        // Fetch article details via AJAX
        fetch(`/admin/articles/${articleId}/details`)
            .then(response => response.json())
            .then(data => {
                const statusBadge = getStatusBadge(data.status);
                const imageHtml = data.image_url ?
                    `<img src="${data.image_url}" alt="${data.title}" class="w-full h-64 object-cover rounded-lg mb-6" onerror="this.style.display='none';">` : '';

                document.getElementById('articleModalContent').innerHTML = `
                    <div class="space-y-6">
                        <!-- Header -->
                        <div class="border-b border-gray-200 pb-4">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="text-xl font-bold text-gray-900">${data.title}</h4>
                                ${statusBadge}
                            </div>
                            <div class="flex items-center text-sm text-gray-500">
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs mr-3">${data.category.name}</span>
                                <span>Created: ${data.created_at}</span>
                                ${data.created_at !== data.updated_at ? `<span class="mx-2">•</span><span>Updated: ${data.updated_at}</span>` : ''}
                            </div>
                        </div>

                        <!-- Author Info -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h5 class="font-medium text-gray-900 mb-2">Author Information</h5>
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-blue-600 font-semibold text-sm">${data.author.name.charAt(0)}</span>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">${data.author.name}</p>
                                    <p class="text-sm text-gray-500">${data.author.email} • ${data.author.role}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Featured Image -->
                        ${imageHtml}

                        <!-- Content -->
                        <div>
                            <h5 class="font-medium text-gray-900 mb-3">Article Content</h5>
                            <div class="prose max-w-none bg-gray-50 rounded-lg p-4">
                                <div class="whitespace-pre-wrap text-gray-800">${data.content}</div>
                            </div>
                        </div>

                        <!-- Actions -->
                        ${data.status === 'pending' ? `
                            <div class="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
                                <form action="/admin/articles/${data.id}/approve" method="POST" class="inline">
                                    <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').getAttribute('content')}">
                                    <input type="hidden" name="_method" value="PUT">
                                    <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                                        Approve Article
                                    </button>
                                </form>
                                <form action="/admin/articles/${data.id}/reject" method="POST" class="inline">
                                    <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').getAttribute('content')}">
                                    <input type="hidden" name="_method" value="PUT">
                                    <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                                        Reject Article
                                    </button>
                                </form>
                            </div>
                        ` : ''}
                    </div>
                `;
            })
            .catch(error => {
                console.error('Error fetching article details:', error);
                document.getElementById('articleModalContent').innerHTML = `
                    <div class="text-center py-12">
                        <div class="text-red-600 mb-2">
                            <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <p class="text-gray-600">Failed to load article details. Please try again.</p>
                        <button onclick="viewArticle(${articleId})" class="mt-3 text-blue-600 hover:text-blue-700">Retry</button>
                    </div>
                `;
            });
    }

    function getStatusBadge(status) {
        const badges = {
            'published': '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Published</span>',
            'pending': '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Pending</span>',
            'rejected': '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Rejected</span>'
        };
        return badges[status] || '';
    }

    function closeArticleModal() {
        document.getElementById('articleModal').classList.add('hidden');
    }

    // Auto-hide flash messages
    setTimeout(function() {
        $('.fixed.top-4.right-4').fadeOut();
    }, 5000);

    // Close modal when clicking outside
    document.getElementById('articleModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeArticleModal();
        }
    });
</script>
@endpush
