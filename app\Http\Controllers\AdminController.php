<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Role;
use App\Models\Article;
use App\Models\Education;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class AdminController extends Controller
{
    // Constructor will be handled by route middleware

    public function dashboard()
    {
        $stats = [
            'total_users' => User::count(),
            'pending_articles' => Article::where('status', 'pending')->count(),
            'pending_education' => Education::where('status', 'pending')->count(),
            'published_articles' => Article::where('status', 'published')->count(),
            'published_education' => Education::where('status', 'published')->count(),
        ];

        $recent_users = User::with('role')->latest()->take(5)->get();
        $pending_articles = Article::with(['user', 'category'])->where('status', 'pending')->latest()->take(5)->get();
        $pending_education = Education::with(['user', 'category'])->where('status', 'pending')->latest()->take(5)->get();

        return view('admin.dashboard', compact('stats', 'recent_users', 'pending_articles', 'pending_education'));
    }

    // User Management
    public function users(Request $request)
    {
        $query = User::with('role');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Role filter
        if ($request->filled('role_filter') && $request->role_filter !== '') {
            $query->where('role_id', $request->role_filter);
        }

        $users = $query->paginate(15)->appends($request->query());
        $roles = Role::all();

        // For AJAX requests, return JSON
        if ($request->ajax()) {
            return response()->json([
                'html' => view('admin.users.table', compact('users'))->render(),
                'pagination' => $users->links()->toHtml(),
                'total' => $users->total()
            ]);
        }

        return view('admin.users.index', compact('users', 'roles'));
    }

    public function updateUserRole(Request $request, User $user)
    {
        $request->validate([
            'role_id' => 'required|exists:roles,id'
        ]);

        $user->update(['role_id' => $request->role_id]);

        return redirect()->route('admin.users')
                        ->with('success', 'User role updated successfully.');
    }

    public function verifyUserEmail(User $user)
    {
        // Check if email is already verified
        if ($user->email_verified_at) {
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'User email is already verified.'
                ], 400);
            }

            return redirect()->route('admin.users')
                            ->with('error', 'User email is already verified.');
        }

        // Update email verification timestamp
        $user->update(['email_verified_at' => now()]);

        // Log the admin action for audit purposes
        Log::info('Admin email verification', [
            'admin_id' => Auth::id(),
            'admin_email' => Auth::user()->email,
            'verified_user_id' => $user->id,
            'verified_user_email' => $user->email,
            'timestamp' => now()
        ]);

        // Refresh the user to get the updated email_verified_at
        $user->refresh();

        // For AJAX requests, return JSON response
        if (request()->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'User email verified successfully.',
                'user' => [
                    'id' => $user->id,
                    'email_verified_at' => $user->email_verified_at ? $user->email_verified_at->format('F j, Y \a\t g:i A') : null,
                    'email_verified' => true
                ]
            ]);
        }

        return redirect()->route('admin.users')
                        ->with('success', 'User email verified successfully.');
    }

    public function deleteUser(User $user)
    {
        if ($user->id === Auth::id()) {
            return redirect()->route('admin.users')
                            ->with('error', 'You cannot delete your own account.');
        }

        $user->delete();

        return redirect()->route('admin.users')
                        ->with('success', 'User deleted successfully.');
    }

    public function getUserDetails(User $user)
    {
        $user->load('role');

        // Get user statistics
        $articlesCount = Article::where('user_id', $user->id)->count();
        $educationCount = Education::where('user_id', $user->id)->count();
        $publishedArticles = Article::where('user_id', $user->id)->where('status', 'published')->count();
        $publishedEducation = Education::where('user_id', $user->id)->where('status', 'published')->count();

        return response()->json([
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'role' => $user->role->name,
            'email_verified' => $user->email_verified_at ? true : false,
            'created_at' => $user->created_at->format('F j, Y \a\t g:i A'),
            'last_login' => $user->updated_at->format('F j, Y \a\t g:i A'),
            'statistics' => [
                'total_articles' => $articlesCount,
                'published_articles' => $publishedArticles,
                'total_education' => $educationCount,
                'published_education' => $publishedEducation
            ]
        ]);
    }

    // Article Management
    public function articles(Request $request)
    {
        $query = Article::with(['user', 'category']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  })
                  ->orWhereHas('category', function($catQuery) use ($search) {
                      $catQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Status filter
        if ($request->filled('status_filter') && $request->status_filter !== 'all') {
            $query->where('status', $request->status_filter);
        }

        $articles = $query->latest()->paginate(15)->appends($request->query());

        // For AJAX requests, return JSON
        if ($request->ajax()) {
            return response()->json([
                'html' => view('admin.articles.table', compact('articles'))->render(),
                'pagination' => $articles->links()->toHtml(),
                'total' => $articles->total()
            ]);
        }

        return view('admin.articles.index', compact('articles'));
    }

    public function approveArticle(Article $article)
    {
        $article->update(['status' => 'published']);

        return redirect()->route('admin.articles')
                        ->with('success', 'Article approved and published successfully.');
    }

    public function rejectArticle(Article $article)
    {
        $article->update(['status' => 'rejected']);

        return redirect()->route('admin.articles')
                        ->with('success', 'Article rejected successfully.');
    }

    public function deleteArticle(Article $article)
    {
        $article->delete();

        return redirect()->route('admin.articles')
                        ->with('success', 'Article deleted successfully.');
    }

    public function getArticleDetails(Article $article)
    {
        $article->load(['user', 'category']);

        return response()->json([
            'id' => $article->id,
            'title' => $article->title,
            'content' => $article->content,
            'image_url' => $article->image_url,
            'status' => $article->status,
            'created_at' => $article->created_at->format('F j, Y \a\t g:i A'),
            'updated_at' => $article->updated_at->format('F j, Y \a\t g:i A'),
            'author' => [
                'name' => $article->user->name,
                'email' => $article->user->email,
                'role' => $article->user->role->name
            ],
            'category' => [
                'name' => $article->category->name,
                'type' => $article->category->type
            ]
        ]);
    }

    // Education Management
    public function education(Request $request)
    {
        $query = Education::with(['user', 'category']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  })
                  ->orWhereHas('category', function($catQuery) use ($search) {
                      $catQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Status filter
        if ($request->filled('status_filter') && $request->status_filter !== 'all') {
            $query->where('status', $request->status_filter);
        }

        $educations = $query->latest()->paginate(15)->appends($request->query());

        // For AJAX requests, return JSON
        if ($request->ajax()) {
            return response()->json([
                'html' => view('admin.education.table', compact('educations'))->render(),
                'pagination' => $educations->links()->toHtml(),
                'total' => $educations->total()
            ]);
        }

        return view('admin.education.index', compact('educations'));
    }

    public function approveEducation(Education $education)
    {
        $education->update(['status' => 'published']);

        return redirect()->route('admin.education')
                        ->with('success', 'Education content approved and published successfully.');
    }

    public function rejectEducation(Education $education)
    {
        $education->update(['status' => 'rejected']);

        return redirect()->route('admin.education')
                        ->with('success', 'Education content rejected successfully.');
    }

    public function deleteEducation(Education $education)
    {
        $education->delete();

        return redirect()->route('admin.education')
                        ->with('success', 'Education content deleted successfully.');
    }

    public function getEducationDetails(Education $education)
    {
        $education->load(['user', 'category']);

        return response()->json([
            'id' => $education->id,
            'title' => $education->title,
            'content' => $education->content,
            'image_url' => $education->image_url,
            'video_url' => $education->video_url,
            'status' => $education->status,
            'created_at' => $education->created_at->format('F j, Y \a\t g:i A'),
            'updated_at' => $education->updated_at->format('F j, Y \a\t g:i A'),
            'author' => [
                'name' => $education->user->name,
                'email' => $education->user->email,
                'role' => $education->user->role->name
            ],
            'category' => [
                'name' => $education->category->name,
                'type' => $education->category->type
            ]
        ]);
    }
}
