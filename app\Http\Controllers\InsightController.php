<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Insight;
use Illuminate\Support\Facades\Auth;

class InsightController extends Controller
{
    /**
     * Store a newly created insight in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'content' => 'required|string|max:1000',
            // In a real application, category_id should be validated
            // against the categories table
            'category_id' => 'required|integer', 
        ]);

        Insight::create([
            'user_id' => Auth::id(),
            'title' => substr($request->content, 0, 50), // Simple title generation
            'content' => $request->content,
            'category_id' => $request->category_id,
            'status' => 'published' // Or 'pending' for admin approval
        ]);

        return back()->with('success', 'Insight published successfully!');
    }
}
