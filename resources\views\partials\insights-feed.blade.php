@forelse ($insights as $insight)
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-all duration-200 group">
        <!-- Author Header -->
        <div class="flex items-center mb-4">
            <div class="w-12 h-12 rounded-full bg-gradient-to-br from-blue-100 to-blue-200 mr-4 flex items-center justify-center flex-shrink-0 group-hover:from-blue-200 group-hover:to-blue-300 transition-all">
                <span class="text-blue-600 font-bold text-sm">{{ substr($insight->user->name, 0, 1) }}</span>
            </div>
            <div class="flex-1 min-w-0">
                <div class="flex flex-col sm:flex-row sm:items-center gap-2">
                    <h4 class="font-bold text-gray-900 text-base">{{ $insight->user->name }}</h4>
                    <div class="flex items-center space-x-2">
                        <span class="text-xs text-blue-600 bg-blue-100 px-3 py-1 rounded-full font-medium">{{ $insight->category->name }}</span>
                        <span class="text-xs text-gray-500">•</span>
                        <span class="text-xs text-gray-500">{{ $insight->created_at->diffForHumans() }}</span>
                    </div>
                </div>
                <div class="flex items-center mt-1">
                    <svg class="w-3 h-3 text-gray-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    <span class="text-xs text-gray-500">{{ $insight->created_at->format('M j, Y \a\t g:i A') }}</span>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="mb-4">
            <div class="bg-gradient-to-r from-gray-50 to-blue-50 rounded-lg p-4">
                <p class="text-gray-800 leading-relaxed text-base">{{ $insight->content }}</p>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex items-center justify-between pt-4 border-t border-gray-100">
            <div class="flex items-center space-x-6">
                @auth
                    <button onclick="toggleInsightLike({{ $insight->id }})"
                        class="like-btn flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 {{ $insight->isLikedBy(auth()->user()) ? 'text-red-600 bg-red-50 hover:bg-red-100' : 'text-gray-500 hover:text-red-500 hover:bg-red-50' }}"
                        data-insight-id="{{ $insight->id }}">
                        <svg class="w-5 h-5"
                            fill="{{ $insight->isLikedBy(auth()->user()) ? 'currentColor' : 'none' }}"
                            stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                        <span class="text-sm font-medium likes-count">{{ $insight->likes_count }}</span>
                    </button>

                    <button onclick="toggleSave({{ $insight->id }})"
                        class="save-btn flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 {{ $insight->isSavedBy(auth()->user()) ? 'text-green-600 bg-green-50 hover:bg-green-100' : 'text-gray-500 hover:text-green-500 hover:bg-green-50' }}"
                        data-insight-id="{{ $insight->id }}">
                        <svg class="w-5 h-5"
                            fill="{{ $insight->isSavedBy(auth()->user()) ? 'currentColor' : 'none' }}"
                            stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                        </svg>
                        <span class="text-sm font-medium saves-count">{{ $insight->saves_count }}</span>
                    </button>
                @else
                    <div class="flex items-center space-x-2 px-3 py-2 text-gray-400 bg-gray-50 rounded-lg">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                        <span class="text-sm font-medium">{{ $insight->likes_count }}</span>
                    </div>

                    <div class="flex items-center space-x-2 px-3 py-2 text-gray-400 bg-gray-50 rounded-lg">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                        </svg>
                        <span class="text-sm font-medium">{{ $insight->saves_count }}</span>
                    </div>
                @endauth
            </div>
        </div>
    </div>
@empty
    <div class="bg-gradient-to-br from-white to-gray-50 rounded-xl border-2 border-dashed border-gray-200 p-12 text-center">
        <div class="w-16 h-16 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center">
            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
            </svg>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-3">No insights found</h3>
        <p class="text-gray-500 mb-6 max-w-md mx-auto">No insights match your search criteria. Try adjusting your search terms or clear the search to see all insights.</p>
        <button onclick="clearSearch()" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium">
            Clear Search
        </button>
    </div>
@endforelse
