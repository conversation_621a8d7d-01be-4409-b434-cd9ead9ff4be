@forelse($educations as $education)
    <div class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow">
        <div class="relative">
            @if($education->image_url)
                <img src="{{ $education->image_url }}"
                     alt="{{ $education->title }}"
                     class="w-full h-48 object-cover"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                <div class="w-full h-48 bg-gradient-to-br from-blue-400 to-blue-500 flex items-center justify-center" style="display: none;">
                    <span class="text-white text-2xl font-bold">{{ substr($education->title, 0, 1) }}</span>
                </div>
            @else
                <div class="w-full h-48 bg-gradient-to-br from-blue-400 to-blue-500 flex items-center justify-center">
                    <span class="text-white text-2xl font-bold">{{ substr($education->title, 0, 1) }}</span>
                </div>
            @endif

            @if($education->video_url)
                <div class="absolute top-3 right-3 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs flex items-center">
                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 6a2 2 0 012-2h6l2 2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"/>
                    </svg>
                    Video
                </div>
            @endif
        </div>

        <div class="p-6">
            <div class="flex items-center mb-3">
                <span class="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">{{ $education->category->name }}</span>
                <span class="text-xs text-gray-500 ml-auto">{{ $education->created_at->format('M d, Y') }}</span>
            </div>

            <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">{{ $education->title }}</h3>
            <p class="text-gray-600 text-sm mb-4 line-clamp-3">{{ Str::limit(strip_tags($education->content), 120) }}</p>

            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-6 h-6 rounded-full bg-blue-100 mr-2 flex items-center justify-center">
                        <span class="text-blue-600 font-semibold text-xs">{{ substr($education->user->name, 0, 1) }}</span>
                    </div>
                    <span class="text-sm text-gray-500">{{ $education->user->name }}</span>
                </div>

                <div class="flex items-center space-x-2">
                    @auth
                        <button onclick="toggleEducationSave({{ $education->id }})"
                                class="education-save-btn text-gray-500 hover:text-blue-500 transition-colors {{ $education->isSavedBy(auth()->id()) ? 'text-blue-500' : '' }}"
                                data-education-id="{{ $education->id }}">
                            <svg class="w-4 h-4" fill="{{ $education->isSavedBy(auth()->id()) ? 'currentColor' : 'none' }}" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"/>
                            </svg>
                        </button>
                    @endauth
                    <a href="{{ route('education.show', $education) }}" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                        Read More →
                    </a>
                </div>
            </div>
        </div>
    </div>
@empty
    <div class="col-span-full bg-white rounded-lg shadow-sm p-12 text-center">
        <div class="w-16 h-16 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center">
            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
            </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No education content found</h3>
        <p class="text-gray-500 mb-6">No education content matches your search criteria. Try adjusting your search terms or clear the search to see all content.</p>
        <button onclick="clearSearch()" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium">
            Clear Search
        </button>
    </div>
@endforelse
