<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Article extends Model
{
    use HasFactory;

    protected $fillable = ['user_id', 'title', 'content', 'category_id', 'image_url', 'status'];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function saves()
    {
        return $this->hasMany(ArticleSave::class);
    }

    public function savedBy()
    {
        return $this->belongsToMany(User::class, 'article_saves');
    }

    // Check if article is saved by a specific user
    public function isSavedBy($userId)
    {
        return $this->saves()->where('user_id', $userId)->exists();
    }
}
