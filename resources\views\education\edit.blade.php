@extends('layout')

@section('title', 'Edit Education Content - InsightFlow')

@section('content')
<div class="p-8">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Edit Education Content</h1>
                    <p class="text-gray-600 mt-2">Update your educational content</p>
                </div>
                <a href="{{ route('education.my-content') }}" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                    Back to My Content
                </a>
            </div>
        </div>

        <!-- Form -->
        <div class="bg-white rounded-lg shadow-sm p-8">
            <form action="{{ route('education.update', $education) }}" method="POST" class="space-y-6">
                @csrf
                @method('PUT')
                
                <!-- Title -->
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Title *</label>
                    <input type="text" 
                           id="title" 
                           name="title" 
                           value="{{ old('title', $education->title) }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('title') border-red-500 @enderror"
                           placeholder="Enter a compelling title for your educational content"
                           required>
                    @error('title')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Category -->
                <div>
                    <label for="category_id" class="block text-sm font-medium text-gray-700 mb-2">Category *</label>
                    <input type="hidden" name="category_id" id="category_id" value="{{ old('category_id', $education->category_id) }}" required>
                    <x-dropdown
                        trigger="{{ $categories->firstWhere('id', old('category_id', $education->category_id))->name ?? 'Select a category' }}"
                        :icon="true"
                        button-class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white hover:bg-gray-50 text-gray-700 justify-between @error('category_id') border-red-500 @enderror"
                        width="w-full"
                    >
                        <x-dropdown-item onclick="selectEducationCategory('', 'Select a category')">
                            Select a category
                        </x-dropdown-item>
                        @foreach($categories as $category)
                            <x-dropdown-item onclick="selectEducationCategory('{{ $category->id }}', '{{ $category->name }}')">
                                {{ $category->name }}
                            </x-dropdown-item>
                        @endforeach
                    </x-dropdown>
                    @error('category_id')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Image URL -->
                <div>
                    <label for="image_url" class="block text-sm font-medium text-gray-700 mb-2">Featured Image URL</label>
                    <input type="url"
                           id="image_url"
                           name="image_url"
                           value="{{ old('image_url', $education->image_url) }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('image_url') border-red-500 @enderror"
                           placeholder="https://example.com/image.jpg">
                    @error('image_url')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                    <p class="text-gray-500 text-sm mt-1">Optional: Add a featured image to make your content more engaging</p>
                </div>

                <!-- Video URL -->
                <div>
                    <label for="video_url" class="block text-sm font-medium text-gray-700 mb-2">Educational Video URL</label>
                    <input type="url"
                           id="video_url"
                           name="video_url"
                           value="{{ old('video_url', $education->video_url) }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('video_url') border-red-500 @enderror"
                           placeholder="https://youtube.com/watch?v=... or https://vimeo.com/...">
                    @error('video_url')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                    <p class="text-gray-500 text-sm mt-1">Optional: Add an educational video (YouTube, Vimeo, etc.) to enhance learning</p>
                </div>

                <!-- Content -->
                <div>
                    <label for="content" class="block text-sm font-medium text-gray-700 mb-2">Content *</label>
                    <textarea id="content" 
                              name="content" 
                              rows="15"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('content') border-red-500 @enderror"
                              placeholder="Write your educational content here. Provide detailed, helpful information that will help others learn about financial topics."
                              required>{{ old('content', $education->content) }}</textarea>
                    @error('content')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                    <p class="text-gray-500 text-sm mt-1">Provide detailed, helpful content that educates readers about financial topics</p>
                </div>

                <!-- Status Notice -->
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-yellow-600 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                        </svg>
                        <div>
                            <h4 class="text-yellow-800 font-medium">Content Status: {{ ucfirst($education->status) }}</h4>
                            <p class="text-yellow-700 text-sm mt-1">
                                @if($education->status === 'pending')
                                    This content is currently pending admin approval. You can edit it until it's reviewed.
                                @elseif($education->status === 'rejected')
                                    This content was rejected. Please review and improve the content before resubmitting.
                                @else
                                    This content has been published and is visible to all users.
                                @endif
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex items-center justify-between pt-6 border-t">
                    <a href="{{ route('education.my-content') }}" class="text-gray-600 hover:text-gray-800">
                        Cancel
                    </a>
                    <button type="submit" class="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                        Update Content
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function selectEducationCategory(categoryId, categoryName) {
    document.getElementById('category_id').value = categoryId;
    // Update the dropdown trigger text
    const trigger = document.querySelector('[x-ref="button"] span');
    if (trigger) {
        trigger.textContent = categoryName;
    }
}
</script>
@endpush
