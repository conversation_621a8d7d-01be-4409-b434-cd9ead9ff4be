<div class="space-y-3">
    @forelse($mostLikedInsights as $insight)
        <div class="bg-gradient-to-r from-red-50 to-pink-50 border-l-4 border-red-400 rounded-r-lg p-3 hover:shadow-sm transition-shadow">
            <p class="text-sm text-gray-800 line-clamp-2 mb-2">{{ Str::limit($insight->content, 60) }}</p>
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <img src="{{ $insight->user->avatar ?? 'https://ui-avatars.com/api/?name=' . urlencode($insight->user->name) . '&color=7c3aed&background=ede9fe' }}" 
                         alt="{{ $insight->user->name }}" 
                         class="w-5 h-5 rounded-full mr-2">
                    <span class="text-xs text-gray-600">{{ $insight->user->name }}</span>
                </div>
                <div class="flex items-center text-red-500">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                    </svg>
                    <span class="text-xs font-medium">{{ $insight->likes_count }}</span>
                </div>
            </div>
        </div>
    @empty
        <div class="text-center py-4">
            <p class="text-gray-500 text-sm">No liked insights yet</p>
        </div>
    @endforelse
</div>
