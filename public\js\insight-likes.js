/**
 * InsightFlow - Insight Like Functionality
 * Provides AJAX-based like/unlike functionality with real-time updates
 */

// Global like functionality with loading states and error handling
window.toggleInsightLike = function(insightId) {
    const btn = $(`.like-btn[data-insight-id="${insightId}"]`);
    const originalHtml = btn.html();
    
    // Prevent multiple clicks during request
    if (btn.hasClass('loading')) {
        return;
    }
    
    // Add loading state
    btn.addClass('loading').prop('disabled', true);
    
    // Show loading spinner
    const currentCount = btn.find('.likes-count').text();
    const loadingSpinner = `
        <svg class="w-5 h-5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
        <span class="text-sm likes-count">${currentCount}</span>
    `;
    
    btn.html(loadingSpinner);
    
    // Make AJAX request
    $.post(`/insights/${insightId}/like`)
        .done(function(data) {
            if (data.success) {
                // Update button state
                updateLikeButton(btn, data.liked, data.likes_count);
                
                // Update most liked insights sidebar if it exists
                if (data.most_liked_html && $('.most-liked-container').length) {
                    $('.most-liked-container').html(data.most_liked_html);
                    
                    // Add fade-in animation
                    $('.most-liked-container').hide().fadeIn(300);
                }
                
                // Show success toast
                showToast(data.liked ? 'success' : 'info', data.message);
                
                // Add subtle animation to the button
                btn.addClass('animate-pulse');
                setTimeout(() => {
                    btn.removeClass('animate-pulse');
                }, 600);

                // Add heart animation for likes
                if (data.liked) {
                    btn.addClass('animate-bounce');
                    setTimeout(() => {
                        btn.removeClass('animate-bounce');
                    }, 500);
                }
                
            } else {
                // Handle server-side errors
                showToast('error', data.message || 'An error occurred');
                btn.html(originalHtml);
            }
        })
        .fail(function(xhr) {
            // Handle network/HTTP errors
            let errorMessage = 'Network error occurred';
            
            if (xhr.status === 401) {
                errorMessage = 'Please log in to like insights';
            } else if (xhr.status === 403) {
                errorMessage = 'You are not authorized to perform this action';
            } else if (xhr.status === 404) {
                errorMessage = 'Insight not found';
            } else if (xhr.status >= 500) {
                errorMessage = 'Server error occurred. Please try again later';
            }
            
            showToast('error', errorMessage);
            btn.html(originalHtml);
        })
        .always(function() {
            // Remove loading state
            btn.removeClass('loading').prop('disabled', false);
        });
};

// Helper function to update like button appearance
function updateLikeButton(btn, liked, likesCount) {
    // Create the updated button content
    const heartIcon = `
        <svg class="w-5 h-5" fill="${liked ? 'currentColor' : 'none'}" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
        </svg>
        <span class="text-sm likes-count">${likesCount}</span>
    `;

    // Update button content
    btn.html(heartIcon);
    
    // Update button styling based on liked state
    if (liked) {
        btn.removeClass('text-gray-500 hover:text-red-500 hover:bg-red-50')
           .addClass('text-red-600 bg-red-50 hover:bg-red-100');
    } else {
        btn.removeClass('text-red-600 bg-red-50 hover:bg-red-100')
           .addClass('text-gray-500 hover:text-red-500 hover:bg-red-50');
    }
}

// Initialize like functionality when document is ready
$(document).ready(function() {
    // Add hover effects to like buttons
    $(document).on('mouseenter', '.like-btn:not(.loading)', function() {
        $(this).addClass('scale-105 transition-transform duration-200');
    });
    
    $(document).on('mouseleave', '.like-btn:not(.loading)', function() {
        $(this).removeClass('scale-105');
    });
    
    // Prevent double-clicks on like buttons
    $(document).on('click', '.like-btn', function(e) {
        if ($(this).hasClass('loading')) {
            e.preventDefault();
            return false;
        }
    });
});

// Utility function to refresh most liked insights (can be called externally)
window.refreshMostLikedInsights = function() {
    if ($('.most-liked-container').length) {
        $.get('/api/most-liked-insights')
            .done(function(data) {
                if (data.success && data.html) {
                    $('.most-liked-container').html(data.html);
                }
            })
            .fail(function() {
                console.warn('Failed to refresh most liked insights');
            });
    }
};
